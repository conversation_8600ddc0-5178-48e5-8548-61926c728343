include:
  - { project: 'prismamediadata/prisma-docker-runtime', file: '/cloudrun-runtime/ci-tools/templates.yml', ref: '1.0.8' }
#-----------------------------------------------------------------
#    VARIABLES
#-----------------------------------------------------------------
variables:
  CLOUD_RUN_SA: cloud-run-rogue-one-sf@pm-${ENV}-app-email.iam.gserviceaccount.com
  CLOUD_RUN_PROJECT_ID: pm-${ENV}-app-email
  CLOUD_RUN_SERVICE_NAME: rogue-one-sf
  PHP_VERSION: '8.3'
  FRAMEWORK: symfony


stages:
  - QA
  - BUILD
  - DEPLOY
  - PROMOTE
  - ROLLBACK

#-----------------------------------------------------------------
#   TEMPLATES
#-----------------------------------------------------------------

## PREPROD TAGS
.preprod_tags: &preprod_tags
  environment: PREPROD
  variables:
    ENV: preprod
    CLOUD_SQL_DB: pm-${ENV}-matrix:europe-west1:${ENV}-matrix-db-9-6
  tags:
    - GCP
    - DEPLOY
    - IT-DATA
    - PREPROD
  only:
    - symfony

## PROD TAGS
.prod_tags: &prod_tags
  environment: PROD
  variables:
    ENV: prod
    CLOUD_SQL_DB: pm-${ENV}-matrix:europe-west1:matrix-db-9-6
  tags:
    - GCP
    - DEPLOY
    - IT-DATA
    - PROD
  only:
    - master

## BUILD BACK
.build_back: &build_back
  extends: .build_back_symfony
  stage: BUILD

## BUILD APP
.build_app: &build_app
  extends: .build_docker_image
  stage: BUILD

## DEPLOY
.deploy: &deploy
  extends: .deploy_cloud_run_service_file
  stage: DEPLOY

## PROMOTE
.promote: &promote
  extends: .promote_cloud_run
  stage: PROMOTE
  when:
    manual

## ROLLBACK
.rollback: &rollback
  extends: .rollback_cloud_run
  stage: ROLLBACK
  when:
    manual

#-----------------------------------------------------------------
#   QA
#-----------------------------------------------------------------
.qa_code_quality: &qa_code_quality
  image: php:$PHP_VERSION
  stage: QA
  environment: PREPROD
  tags:
    - GCP
    - DEPLOY
    - IT-DATA
    - PREPROD
  before_script:
    - su -s /bin/sh www-data
    - echo $CI_ENVIRONMENT_NAME
    - cd $CI_PROJECT_DIR/
    - mkdir -p reports
  script:
    - su -s /bin/sh www-data
    - sh scripts/ci-php-install.sh
    - php -v
    - su -s /bin/sh -c 'composer fund --version' www-data
    - su -s /bin/sh -c 'composer install -n --prefer-dist --no-progress --ansi --classmap-authoritative --ignore-platform-reqs --no-scripts --no-cache' www-data
    - su -s /bin/sh -c 'composer install -n --prefer-dist --no-progress --ansi --classmap-authoritative --working-dir=./tools/php-cs-fixer --no-scripts --no-cache' www-data
    - cp .env.local.dist .env.local
    - echo "Launch PHPCsFixer Check"
    - su -s /bin/sh -c 'composer cs-check' www-data
  cache:
    - key: { files: [tools/php-cs-fixer/composer.lock] }
      paths: [tools/php-cs-fixer/vendor]
    - key: { files: [composer.lock] }
      paths: [vendor]
  artifacts:
    paths:
      - reports
    expire_in: 24 hrs
    when: on_failure

#-----------------------------------------------------------------
#   BUILD
#-----------------------------------------------------------------
📦:build:back:preprod:
  <<: *preprod_tags
  <<: *build_back

📦:build:back:prod:
  <<: *prod_tags
  <<: *build_back

🐳:build:image:preprod:
  <<: *preprod_tags
  <<: *build_app
  needs: ["📦:build:back:preprod"]
  dependencies: ["📦:build:back:preprod"]

🐳:build:image:prod:
  <<: *prod_tags
  <<: *build_app
  needs: ["📦:build:back:prod"]
  dependencies: ["📦:build:back:prod"]

#-----------------------------------------------------------------
# QA CODE QUALITY
#-----------------------------------------------------------------
🧪:qa:codequality:app:
  <<: *qa_code_quality
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "symfony"'

#-----------------------------------------------------------------
#   DEPLOY SERVICE
#-----------------------------------------------------------------
🚀:deploy:preprod:
  <<: *preprod_tags
  <<: *deploy
  needs: ["🐳:build:image:preprod"]

🚀:deploy:prod:
  <<: *prod_tags
  <<: *deploy
  needs: ["🐳:build:image:prod"]

#-----------------------------------------------------------------
#   PROMOTE / SPLIT TRAFFIC SERVICE = DEFAULT
#-----------------------------------------------------------------
✅:promote:preprod:
  <<: *preprod_tags
  <<: *promote
  needs: ["🚀:deploy:preprod"]

✅:promote:prod:
  <<: *prod_tags
  <<: *promote
  needs: ["🚀:deploy:prod"]

🔂:rollback:preprod:
  <<: *preprod_tags
  <<: *rollback
  needs: ["✅:promote:preprod"]
  dependencies: ["✅:promote:preprod"]

🔂:rollback:prod:
  <<: *prod_tags
  <<: *rollback
  needs: ["✅:promote:prod"]
  dependencies: ["✅:promote:prod"]

