# Sync societies from Picbi API
# Can be used in different ways:
# 1. Sync all societies (deletes the ones that are currently in database)
* * * * * admro php /home/<USER>/dev-rogue-one.pdata.fr/bin/console.php sync-picbi-society

# 2. Sync all new societies that do not exist in database
*/15 * * * * admro php /home/<USER>/dev-rogue-one.pdata.fr/bin/console.php sync-picbi-society --sync-new

# 3. Sync all modifications since that latest time this command was executed
10,25,40,55 * * * * admro php /home/<USER>/dev-rogue-one.pdata.fr/bin/console.php sync-picbi-society --sync-modify


# Sync all actors from Picbi API
0 * * * * admro php /home/<USER>/dev-rogue-one.pdata.fr/bin/console.php sync-picbi-actor

# Sync all kantars from Picbi API
0 * * * * admro php /home/<USER>/dev-rogue-one.pdata.fr/bin/console.php sync-kantar


# Sync quotations from Picbi API
# Can be used in different ways:
# 1. Sync all quotations (deletes the ones that are currently in database)
* * * * * admro php /home/<USER>/dev-rogue-one.pdata.fr/bin/console.php sync-picbi-quotation

# 2. Sync all new quotations that do not exist in database
* * * * * admro php /home/<USER>/dev-rogue-one.pdata.fr/bin/console.php sync-picbi-quotation --sync-new

# 3. Sync all modifications since that latest time this command was executed
* * * * * admro php /home/<USER>/dev-rogue-one.pdata.fr/bin/console.php sync-picbi-quotation --sync-modify


# Import stats from csv files. Stores raw data in Mongo
# * * * * * admro test `ps -aef | grep import-stats | wc -l` -le 3 && php /home/<USER>/dev-rogue-one.pdata.fr/bin/console.php import-stats

# Groups stats data by email Id
# * * * * * admro test `ps -aef | grep aggregate-stats | wc -l` -le 3 && php /home/<USER>/dev-rogue-one.pdata.fr/bin/console.php aggregate-stats
30 * * * * admro test `ps -aef | grep update-stats | wc -l` -le 3 && sh /home/<USER>/dev-rogue-one.pdata.fr/bin/cron.update-stats.sh

# Get all remote stats data logs
10,20  * * * * admro sh /home/<USER>/rogue-one.pdata.fr/bin/cron.get-remote-data.sh
# Update Stats
0 * * * * admro php /home/<USER>/rogue-one.pdata.fr/bin/console.php update-stats

# Purge stat lines from a given date
# --from=<date> : Can be used with any strtotime parameter, being a precise date, or something like "-1 year"
#                 Date cannot be purged under 1 month. Parameter MUST be placed between quotes
* * * * * admro php /home/<USER>/dev-rogue-one.pdata.fr/bin/console.php purge-stat-lines

# Sync pcp-manager and rogue-one emails
* * * * * admro test `ps -aef | grep sync-email | wc -l` -le 3 && php /home/<USER>/rogue-one.pdata.fr/bin/console.php sync-email >>/home/<USER>/rogue-one.pdata.fr/data/cron.sync-email.log 2>&1

# Export Stats for the last 3 months (needed for Picbi)
0 6 * * * admro php /home/<USER>/rogue-one.pdata.fr/bin/console.php export-stats >>/home/<USER>/rogue-one.pdata.fr/data/cron.export-stats.log 2>&1

# Sync Stats Invoice
0 8 * * * admro php /home/<USER>/rogue-one.pdata.fr/bin/console.php sync-stat-invoice >>/home/<USER>/rogue-one.pdata.fr/data/cron.sync-stat-invoice.log 2>&1

# update stats sent/delivered for splio
* */4 * * * admro /usr/bin/php /home/<USER>/rogue-one.pdata.fr/bin/console.php get-splio-nl-stats >> /home/<USER>/rogue-one.pdata.fr/data/cron.get-splio-nl-stats.log 2>&1

# Delete all temporary emails created before today -1h
0 * * * * admro php /home/<USER>/rogue-one.pdata.fr/bin/console.php delete-temp-emails

# Update validated past newsletter emails status => SENT, hourlyI
30 23 * * * admro php /home/<USER>/rogue-one.pdata.fr/bin/console.php update-nl-emails

# Auto create NL
0 2 * * * admro php /home/<USER>/rogue-one.pdata.fr/bin/console.php auto-create-nl

# Auto Validate NL
0 5 * * * admro php /home/<USER>/rogue-one.pdata.fr/bin/console.php auto-validate-nl

# Run SpamAsssassin to check emails with shoot_date between today and tomorrow
0 10-20/2 * * * admro php /home/<USER>/rogue-one.pdata.fr/bin/console.php check-spam >> /home/<USER>/rogue-one.pdata.fr/data/cron.check-spam.log 2>&1

