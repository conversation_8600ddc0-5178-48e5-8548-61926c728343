{"php": "8.3.17", "version": "3.58.1:v3.58.1#04e9424025677a86914b9a4944dbbf4060bb0aff", "indent": "    ", "lineEnding": "\n", "rules": {"encoding": true, "full_opening_tag": true, "blank_line_after_namespace": true, "braces_position": {"allow_single_line_anonymous_functions": true, "allow_single_line_empty_anonymous_classes": true}, "class_definition": {"single_line": true}, "constant_case": true, "control_structure_braces": true, "control_structure_continuation_position": true, "elseif": true, "function_declaration": true, "indentation_type": true, "line_ending": true, "lowercase_keywords": true, "method_argument_space": {"on_multiline": "ignore"}, "no_break_comment": true, "no_closing_tag": true, "no_multiple_statements_per_line": true, "no_space_around_double_colon": true, "no_spaces_after_function_name": true, "no_trailing_whitespace": true, "no_trailing_whitespace_in_comment": true, "single_blank_line_at_eof": true, "single_class_element_per_statement": true, "single_import_per_statement": true, "single_line_after_imports": true, "spaces_inside_parentheses": true, "statement_indentation": {"stick_comment_to_next_continuous_control_statement": true}, "switch_case_semicolon_to_colon": true, "switch_case_space": true, "visibility_required": true, "align_multiline_comment": {"comment_type": "all_multiline"}, "array_syntax": {"syntax": "short"}, "backtick_to_shell_exec": true, "binary_operator_spaces": true, "blank_line_before_statement": {"statements": ["return"]}, "class_attributes_separation": {"elements": {"method": "one"}}, "class_reference_name_casing": true, "clean_namespace": true, "concat_space": true, "declare_parentheses": true, "echo_tag_syntax": true, "empty_loop_body": {"style": "braces"}, "empty_loop_condition": true, "fully_qualified_strict_types": true, "general_phpdoc_tag_rename": {"replacements": {"inheritDocs": "inheritDoc"}}, "global_namespace_import": {"import_classes": false, "import_constants": false, "import_functions": false}, "include": true, "increment_style": true, "integer_literal_case": true, "lambda_not_used_import": true, "linebreak_after_opening_tag": true, "magic_constant_casing": true, "magic_method_casing": true, "native_function_casing": true, "native_type_declaration_casing": true, "no_alias_language_construct_call": true, "no_alternative_syntax": true, "no_binary_string": true, "no_blank_lines_after_phpdoc": true, "no_empty_comment": true, "no_empty_phpdoc": true, "no_empty_statement": true, "no_extra_blank_lines": {"tokens": ["attribute", "case", "continue", "curly_brace_block", "default", "extra", "parenthesis_brace_block", "square_brace_block", "switch", "throw", "use"]}, "no_leading_namespace_whitespace": true, "no_mixed_echo_print": true, "no_multiline_whitespace_around_double_arrow": true, "no_null_property_initialization": true, "no_short_bool_cast": true, "no_singleline_whitespace_before_semicolons": true, "no_spaces_around_offset": true, "no_superfluous_phpdoc_tags": {"allow_hidden_params": true, "remove_inheritdoc": true}, "no_trailing_comma_in_singleline": true, "no_unneeded_braces": {"namespaces": true}, "no_unneeded_control_parentheses": {"statements": ["break", "clone", "continue", "echo_print", "others", "return", "switch_case", "yield", "yield_from"]}, "no_unneeded_import_alias": true, "no_unset_cast": true, "no_unused_imports": true, "no_useless_concat_operator": true, "no_useless_nullsafe_operator": true, "no_whitespace_before_comma_in_array": true, "normalize_index_brace": true, "nullable_type_declaration": true, "nullable_type_declaration_for_default_null_value": true, "object_operator_without_whitespace": true, "operator_linebreak": {"only_booleans": true}, "ordered_imports": true, "ordered_types": {"null_adjustment": "always_last", "sort_algorithm": "none"}, "php_unit_fqcn_annotation": true, "php_unit_method_casing": true, "phpdoc_align": true, "phpdoc_indent": true, "phpdoc_inline_tag_normalizer": true, "phpdoc_no_access": true, "phpdoc_no_alias_tag": true, "phpdoc_no_package": true, "phpdoc_no_useless_inheritdoc": true, "phpdoc_order": true, "phpdoc_return_self_reference": true, "phpdoc_scalar": true, "phpdoc_separation": {"groups": [["Annotation", "NamedArgumentConstructor", "Target"], ["author", "copyright", "license"], ["category", "package", "subpackage"], ["property", "property-read", "property-write"], ["deprecated", "link", "see", "since"]]}, "phpdoc_single_line_var_spacing": true, "phpdoc_tag_type": {"tags": {"inheritDoc": "inline"}}, "phpdoc_to_comment": true, "phpdoc_trim": true, "phpdoc_trim_consecutive_blank_line_separation": true, "phpdoc_types": true, "phpdoc_types_order": {"null_adjustment": "always_last", "sort_algorithm": "none"}, "phpdoc_var_without_name": true, "semicolon_after_instruction": true, "simple_to_complex_string_variable": true, "single_line_comment_spacing": true, "single_line_comment_style": {"comment_types": ["hash"]}, "single_line_throw": true, "single_quote": true, "single_space_around_construct": true, "space_after_semicolon": {"remove_in_empty_for_expressions": true}, "standardize_increment": true, "standardize_not_equals": true, "switch_continue_to_break": true, "trailing_comma_in_multiline": true, "trim_array_spaces": true, "type_declaration_spaces": true, "types_spaces": true, "unary_operator_spaces": true, "whitespace_after_comma_in_array": true, "yoda_style": true, "array_indentation": true, "cast_spaces": true, "blank_line_after_opening_tag": true, "blank_line_between_import_groups": true, "blank_lines_before_namespace": true, "compact_nullable_type_declaration": true, "declare_equal_normalize": true, "lowercase_cast": true, "lowercase_static_reference": true, "new_with_parentheses": true, "no_blank_lines_after_class_opening": true, "no_leading_import_slash": true, "no_whitespace_in_blank_line": true, "ordered_class_elements": true, "return_type_declaration": true, "short_scalar_cast": true, "single_trait_insert_per_statement": true, "ternary_operator_spaces": true, "doctrine_annotation_spaces": true, "doctrine_annotation_indentation": true, "doctrine_annotation_braces": true, "combine_consecutive_unsets": true, "phpdoc_add_missing_param_annotation": true}, "hashes": {"src/Dto/EditorViewModel.php": "f83e2bfb9dc3dbf6864a647450f40729", "src/Repository/RepositoryInterface.php": "641a1e8628a6ab13e5b5a6dfffec7bf3", "src/Repository/EditorRepository.php": "da23d42bb2a27aa518ce3ad83710f8af", "src/Repository/TemplateRepository.php": "a75fa7d4ad72f8645a9a05080f1b2912", "src/Repository/BaseRepository.php": "85051ceefd0ce36ac9b92f7ef8a03284", "src/Repository/AbstractRepository.php": "3723cfe6b6e858cb6f8e6440536883ba", "src/Repository/UserRepository.php": "61129498c1e4a4948734cccc303d4922", "src/Form/Type/EditorType.php": "31730491df6c4f06f76fb9313aa0f818", "src/Form/Type/AbstractFormType.php": "85aa7eb29e1f9b737a805d6030585d4c", "src/Form/Type/TemplateType.php": "74b615ca286e3586780033b3d4ff8b57", "src/Form/FormUtils.php": "b14414e42778f5dfc99baa558584ffbf", "src/Validator/AbstractValidator.php": "e773f67e36da0183c801abd8da4c9f4f", "src/Response/ListenerExit.php": "f937e5cf0eb6c17391dc306a417666fe", "src/Security/LocalAuthenticator.php": "84c37ab57c29ad9ee02dc311eb965f8c", "src/Entity/AbstractEntity.php": "d1d8e6b96493f444f52302f008ed985e", "src/Entity/AbstractContent.php": "31ef4a0767f9c383dc9fc81f63cc3cd7", "src/Entity/AbstractStringIdEntity.php": "710936bfd22f2f5b4317dc729219c557", "src/Entity/User.php": "c8efcd17b8907bd35901a039aedf9095", "src/Entity/TimestampableEntityTrait.php": "6de066ce807c32956a8f6c5c2dbf6f2d", "src/Entity/Editor.php": "c92ea97d2fe45dd2676deabf748808e9", "src/Entity/ContentInterface.php": "47ddad79a9460246bac6f7dadf3466dd", "src/Entity/Base.php": "ddb00951b225e955c4d121840c41bd26", "src/Entity/EntityDateInterface.php": "d64d633d047195649994ab7aafe9bd22", "src/Entity/Template.php": "62d7f9a9a728ce7fe012d6fcf9969ca3", "src/Entity/EntityInterface.php": "7843931832de83ba86ff8d019cac7e35", "src/DataFixtures/AppFixtures.php": "809fe1e44211290fc555ffb128f72259", "src/Utils/DateUtils.php": "4ce1bef20c356bbdffb89dbe0cfe9077", "src/Controller/EditorController.php": "02e0b567b918281e47c3749bf62a6fb1", "src/Controller/TemplateController.php": "3ae4508feebba2a2869046b31976871b", "src/Controller/BaseController.php": "e07d2f48b71c2e6dc33c6d083f9ebc4f", "src/Controller/HomepageController.php": "d2fa71d5adcebbe1ab81fd5e5b39d1ed", "src/Filter/FilterInterface.php": "bff2927c3604f281cf9d5a88b0fd6696", "src/Filter/FragmentsInHtml.php": "bdb547f63358f65f7d4c6e9db3c033a4", "src/Service/AbstractService.php": "eb0a246f3742de6ab882168f5ab9e9ea", "src/Service/EditorService.php": "dcdeb24a5437e87b1ddd00342bc5dd92", "src/Service/BaseService.php": "01cff36c4cf2c6567f59fa0ae61b4717", "src/Service/AbstractModularService.php": "e2c41172e7b198fc21089905e467572c", "src/Service/TemplateService.php": "04ae7b786a67e0945d30860dfa14e52a", "src/Twig/NavbarExtension.php": "c4e29ce0dfada7a01523b89b61bce557", "src/EventSubscriber/AbstractDateSubscriber.php": "900f855a2232f5a2fcd56fea6f1bcc6c", "src/EventSubscriber/AbstractSubscriber.php": "9a01f2458bf135a14017fe5be41c2ad5", "src/EventSubscriber/Template/SetTagsSubscriber.php": "01900182dd96082b0843f5e4352603d0", "src/EventSubscriber/Template/DateSubscriber.php": "6985e142ceb92f1f05a08849e22ced52", "src/Event/AbstractEvent.php": "c980950cdda51f2bc8463faba35a9ed6", "src/Event/EventInterface.php": "3ebf496f063d4d2d0a577bd2c1a38d9f", "src/Event/PersistenceEvent.php": "2e6e3d8b005d907d46d1804a26e1f080", "src/Event/Template.php": "dff94ee97ddb5ca39304f23d717e9cd0", "src/Event/EntityEventExtension.php": "b7435d4c0d1ddbc94821b8b89fb358a1", "src/Factory/EditorViewModelFactory.php": "2c8fc6515f9db4f598b6941b91da7716", "src/Factory/ViewModelInterface.php": "87aec4bf76b647db528af70e07fbe889", "src/Factory/ViewModelTrait.php": "d2f3b306325cbd2d95079e9bd069f3b2", "src/Factory/PDOFactory.php": "b29314bf8d136bd7d59b8936fa768eae", "src/DataTable/Type/EditorDataTableType.php": "a9bd216de3e8d668c9b3ca87ea00470a", "src/DataTable/Type/TemplateDataTableType.php": "5f0f384dfe8634e90bd8c2cc8390a802", "src/Kernel.php": "065c02fc2d62bfff3cd16f3a0b46d9ee", "src/DataTable/Type/BaseDataTableType.php": "bf5df614d9a2bcd884fed25c07697787", "src/Form/Type/BaseType.php": "44061ef2ada904e05dd3a59174c655ac", "src/EventSubscriber/Base/DateSubscriber.php": "45529ba0e36078ae3cd99363f920aa09", "src/Event/Base.php": "1e0aa51cc90f3585a488ee3ad05f546b", "src/EventSubscriber/Base/DeleteSubscriber.php": "974d45601df35c3dc3005db31b1576e5", "src/Repository/EmailConsentRepository.php": "ac9292fccdccc26fe30dd2bd2045c11b", "src/Form/DataTransformer/PublicRefTransformer.php": "16b8490fe76220b186a17dff39df06bb", "src/Entity/EmailConsent.php": "449bdb015b87da132b5a7ce69a868508", "src/Twig/RouterUrlExtension.php": "e1b85f0246ca1dea40bb2326a31fd24f", "src/Form/Type/TemplateContentType.php": "61ae1465cef7ac93b9e6b76d1c2670c5", "src/Controller/Api/RouterUrlController.php": "ba951a4c114ea98d5b9eebb9976af1eb", "src/Twig/AceEditorExtension.php": "773f818e2c6a37919e45aa93baf5acd9", "src/ApiController/TemplateController.php": "c4721cc6e2354cb4a619f10c17908009", "src/Controller/Api/TemplateController.php": "7eb50b4bbb349f75d44be1ce9fadd09e", "src/Service/EmailConsentService.php": "db6f3c05cd3fd25a3dfe96d8b083c0f6", "src/Repository/PlanningRepository.php": "9fefae7ea7facd9d499fe344758b371a", "src/Form/Type/PlanningType.php": "d04228cbff1e75025a07a7a73726d274", "src/Entity/Planning.php": "fc57c3505b8b22406676f07e06243d4f", "src/Controller/PlanningController.php": "168fdb45d4f72de2d28d93cee4fa62fd", "src/Service/PlanningService.php": "e46a7cf798e551bdaffc041d899569b2", "src/Twig/PlanningExtension.php": "6e0a39a295d43cda0efbfd40d5daa6b1", "src/DataTable/Type/PlanningDataTableType.php": "753b904c0a3a27a1e31e1a7a6974ee92", "src/Repository/StaticEmailRepository.php": "52a9b6dc9e2c0a432dde338b345db7bc", "src/Form/StaticEmailType.php": "e787b1a0ff1323375a3568819f8d8815", "src/Entity/StaticEmail.php": "9e753a2d6703f42020bbf55ac68e1ebf", "src/Controller/StaticEmailController.php": "9a4d59611d79447b1ff50924437340d0", "src/Service/StaticEmailService.php": "a88b43cfd2aeaf4df990b348bc8d269e", "src/DataTable/Type/StaticEmailDataTableType.php": "0db4f96dfea07bf5acb73c976095e07d", "src/Entity/PicbiBrand.php": "8e3c6bf4fdcbc16f8a76a278750fadab", "src/Entity/PicbiSociety.php": "43ce55408bdafda153213dfdf86d7e90", "src/Entity/PicbiContact.php": "695543ad39a4ce9de14f7cf01b8c1f40", "src/Repository/PicbiSocietyRepository.php": "508253f0102cd993cf29fbf578a8a630", "src/Service/PicbiSocietyService.php": "febc2b8bd50963e14b148e8273844683"}}