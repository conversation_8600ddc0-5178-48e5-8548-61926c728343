apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/ingress-status: all
  labels:
    cloud.googleapis.com/location: europe-west1
    cloud_run_service_id: {{CLOUD_RUN_SERVICE_NAME}}
  name: {{CLOUD_RUN_SERVICE_NAME}}
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: '5'
        autoscaling.knative.dev/minScale: '0'
        run.googleapis.com/client-name: gcloud
        run.googleapis.com/client-version: 462.0.1
        run.googleapis.com/cloudsql-instances: {{CLOUD_SQL_DB}}
        run.googleapis.com/cpu-throttling: "true"
        run.googleapis.com/execution-environment: "gen2"
        run.googleapis.com/sessionAffinity: "true"
        run.googleapis.com/startup-cpu-boost: "true"
        #run.googleapis.com/vpc-access-egress: all-traffic
        #run.googleapis.com/network-interfaces: '[{"network":"app-email","subnetwork":"app-email-europe-west1"}]'
      labels:
        cloud_run_service_id: {{CLOUD_RUN_SERVICE_NAME}}
      name: {{CLOUD_RUN_SERVICE_NAME}}-{{RELEASE_VERSION}}
    spec:
      containerConcurrency: 80
      containers:
        - env:
            - name: APP_DEBUG
              value: '0'
            - name: APPLICATION_ENV
              value: gcp-preproduction
            - name: PSQL_SOCKET
              value: /cloudsql/{{CLOUD_SQL_DB}}
            - name: GOOGLE_PROJECT_ID
              value: {{CLOUD_RUN_PROJECT_ID}}
            - name: GOOGLE_CLOUD_PROJECT
              value: {{CLOUD_RUN_PROJECT_ID}}
            - name: PROJECT_ID
              value: {{CLOUD_RUN_PROJECT_ID}}
            - name: FIRESTORE_PROJECT_ID
              value: {{CLOUD_RUN_PROJECT_ID}}
            - name: FIRESTORE_COLLECTION
              value : {{CLOUD_RUN_SERVICE_NAME}}_sessions
            - name: USE_FIRESTORE
              value: 'on'
            - name: HTTPS
              value: 'on'
            - name: SESSION_TTL
              value: '172800'
            - name: OAUTH_GOOGLE_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: rogue_one_oauth_google_client_id
            - name: OAUTH_GOOGLE_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: rogue_one_oauth_google_client_secret
            - name: PSQL_DB
              value: matrix
            - name: PSQL_USER
              value: rogue_one_app
            - name: CLOUD_RUN_SERVICE
              value: {{CLOUD_RUN_SERVICE_NAME}}
            - name: GCS_CDN_IMAGE_PROJECT_ID
              value: 'pm-preprod-service-image'
            - name: GCS_CDN_IMAGE_BUCKET_ID
              value: 'preprod-prisma-cdn-images'
            - name: NLF_GET_NL_URL
              value: 'https://nl-factory.preprod.prismadata.fr/get-nl/'
            - name: PSQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: psql_rogue_one_app
            - name: BAT_EMAIL_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: rogue_one_bat_email_password
            - name: STATIC_EMAIL_TOKEN_NL_FACTORY
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: rogue_one_static_email_token_nl_factory
            - name: PERSONALIZED_NEWSLETTER_MOZART_API_TOKEN
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: rogue_one_personalized_newsletter_mozart_api_token
            - name: PMC_EMAIL_TOKEN_SECRET
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: rogue_one_pmc_email_token_secret
            - name: SPLIO_SMTP_PRISMA_TRANSAC_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: rogue_one_splio_smtp_prisma_transac_password
            - name: SPLIO_SMTP_CAPITAL_TRANSAC_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: rogue_one_splio_smtp_capital_transac_password
            - name: SPLIO_SMTP_FEMME_ACTUELLE_FWD_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: rogue_one_splio_smtp_femme_actuelle_fwd_password
            - name: SPLIO_SMTP_NL_SHOPPING_FWD_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: rogue_one_splio_smtp_nl_shopping_fwd_password
            - name: ROOT_DOMAIN
              value: 'rogue-one.preprod.prismadata.fr'
          image: {{ARTIFACT_REPO_URL}}
          ports:
            - containerPort: 80
              name: http1
          resources:
            limits:
              cpu: '2'
              memory: 2Gi
          startupProbe:
            failureThreshold: 2
            initialDelaySeconds: 30
            periodSeconds: 240
            tcpSocket:
              port: 80
            timeoutSeconds: 40
      serviceAccountName: cloud-run-{{CLOUD_RUN_SERVICE_NAME}}@pm-{{ENV}}-app-email.iam.gserviceaccount.com
      timeoutSeconds: 1800 # max/default 3600s
  traffic:
    - revisionName: {{OLD_REVISION}}
      tag: {{OLD_REVISION_TAG}}
      percent: 100
    - revisionName: {{CLOUD_RUN_SERVICE_NAME}}-{{RELEASE_VERSION}}
      tag: {{RELEASE_VERSION}}

# deploy cmd: gcloud run services replace --platform=managed  karinto-preprod.yaml --region=europe-west1 --project=$CLOUD_RUN_PROJECT_ID