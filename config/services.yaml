# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
imports:
    - { resource: tags.yaml }

parameters:

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            string $rootDomain: '%env(ROOT_DOMAIN)%'

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    App\Factory\PDOFactory: ~
    pdo:
        class: PDO
        factory: [ 'App\Factory\PDOFactory', 'create' ]
        arguments:
            $username: '%env(PSQL_USER)%'
            $password: '%env(PSQL_PASSWORD)%'
            $dbName: '%env(PSQL_DB)%'
            $instanceUnixSocket: '%env(PSQL_SOCKET)%'
            $port: '%env(PSQL_PORT)%'
        public: false

    App\Form\Type\TemplateContentType:
        arguments:
            $contentTags: '%tags%'
        tags: ['form.type']

    App\Service\TemplateService:
        arguments:
            $nlfGetNlUrl: '%env(NLF_GET_NL_URL)%'
