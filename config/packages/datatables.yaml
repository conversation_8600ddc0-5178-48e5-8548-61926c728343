# Latest documentation available at https://omines.github.io/datatables-bundle/#configuration
datatables:
    # Set options, as documented at https://datatables.net/reference/option/
    options:
        lengthMenu : [10, 25, 50, 100, 250, 500]
        pageLength: 50
        # dom: "<'row' <'col-sm-12' lTfg tr>><'row' <'col-sm-6'i><'col-sm-6 text-right'p>>"
    method: POST
    persist_state: 'local'
    template_parameters:
        # Example classes to integrate nicely with Bootstrap 3.x
        className: 'table table-striped table-bordered table-hover data-table no-footer'
        columnFilter:  "thead"

    # You can for example override this to "tables" to keep the translation domains separated nicely
    translation_domain: 'messages'
    template: '@DataTables/datatable_html.html.twig'