#!/bin/bash

####
# This script parse a yaml file (cron.yaml) using yq package.
#
# yq is a portable command-line YAML, JSON, XML, CSV, TOML and properties processor
# (Document: https://github.com/mikefarah/yq)
#
# From each item found in yaml file, the script construct a gcloud command to create a scheduler job.
#
# Only the headers field is optional for the item

# Below is a typical example of the content of the expected cron.yaml file with 2 items

# cron:
# - description: "job 1"
#   name: job1
#   uri: https://job1.fr
#   schedule: 0 */1 * * *
#   http-method:GET
#   attempt-deadline: 10s
#   headers: X-SECRET-KEY=secret-value
#
# - description: "job 2"
#   name: job2
#   uri: https://job2.fr
#   schedule: 0 */2 5 * *
#   http-method:GET
#   attempt-deadline: 60s
####

project="$2"
yaml_file="$1"

# install yq package
apk add --no-cache yq

scheduler_job_gcloud_command() {
    local name="$1"
    local schedule="$2"
    local uri="$3"
    local http_method="$4"
    local attempt_deadline="$5"
    local headers="$6"
    local project="$7"
    local description="$8"

    # check job name if already created
    job_info=$(gcloud scheduler jobs describe "$name" --project="$project" --location=europe-west1 2>/dev/null)

    if [ -n "$job_info" ]; then
      command="gcloud scheduler jobs update http $name --description=\"$description\" --schedule=\"$schedule\" --uri=$uri --http-method=$http_method --attempt-deadline=$attempt_deadline --project=$project --time-zone=Europe/Paris --location=europe-west1 --verbosity=none"
    else
      command="gcloud scheduler jobs create http $name --description=\"$description\" --schedule=\"$schedule\" --uri=$uri --http-method=$http_method --attempt-deadline=$attempt_deadline --project=$project --time-zone=\"Europe/Paris\" --location=europe-west1 --verbosity=none"

      if [ -n "$headers" ]; then
          command+=" --headers=\"$headers\""
      fi
    fi

    echo "$command"
}

# Parse YAML file and generate commands
parse_yaml() {
    local yaml_file="$1"
    local project="$2"

    echo "yaml_file: $yaml_file, project: $project"

    # shellcheck disable=SC2155
    local jobs=$(yq eval '.cron' "$yaml_file")

    # Count the number of jobs
    # shellcheck disable=SC2155
    local num_jobs=$(echo "$jobs" | yq eval '. | length')

    if (( num_jobs <= 0 )); then
      # Code to execute if num_jobs is less than or equal to zero
      echo "There are no jobs found in the $yaml_file file"
    else
      echo "There are $num_jobs jobs found in the $yaml_file file"
      # shellcheck disable=SC2004
      i=0
      while [ "$i" -le "$num_jobs" ]; do
          job=$(echo "$jobs" | yq eval ".[$i]" -)
          # shellcheck disable=SC2028
          echo
          echo "----------------"
          echo "Processing job $i:"
          echo "$job "
          echo ""
          description=$(echo "$job" | yq eval '.description // ""' -)
          name=$(echo "$job" | yq eval '.name // ""' -)
          http_method=$(echo "$job" | yq eval '.["http-method"] // ""' -)
          attempt_deadline=$(echo "$job" | yq eval '.["attempt-deadline"] // ""' -)
          schedule=$(echo "$job" | yq eval '.schedule // ""' -)
          uri=$(echo "$job" | yq eval '.uri // ""' -)
          headers=$(echo "$job" | yq eval '.headers // ""' -)

          echo "uri: $uri, name: $name, schedule: $schedule"
          if [[ -z "$uri" ]]; then
            break
          fi

          command=$(scheduler_job_gcloud_command "$name" "$schedule" "$uri" "$http_method" "$attempt_deadline" "$headers" "$project" "$description")
          echo "Command for $description:"
          echo ""
          echo "command :  $command"
          echo
          echo "-> Execute the create/update scheduler job command..."
          eval "$command"
          echo
          i=$((i + 1))
      done
    fi
}

# Main
if [ "$#" -lt 2 ]; then
    echo "Usage: $0 <yaml_file> <project>"
    exit 1
fi

# Main
parse_yaml "$yaml_file" "$project"
