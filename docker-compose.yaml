version: "3.9"
services:
  rogueone-nginx:
    image: nginx:latest
    volumes:
      - ./docker/nginx/config/nginx.conf.tpl:/nginx.conf.tpl
      - ./public:${PWD}/public
    labels:
      - traefik.enable=true
      - traefik.http.services.rogueone-front.loadbalancer.server.port=80
      - traefik.http.routers.http-rogueone-front.rule=HostRegexp(`rogue-one.local.com`, `local.rogue-one.prismadata.fr`)
      - traefik.http.routers.http-rogueone-front.entrypoints=http
      - traefik.http.routers.http-rogueone-front.middlewares=redirect-to-https
      - traefik.http.routers.https-rogueone-front.rule=HostRegexp(`rogue-one.local.com`, `local.rogue-one.prismadata.fr`)
      - traefik.http.routers.https-rogueone-front.entrypoints=https
      - traefik.http.routers.https-rogueone-front.tls=true
      - traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https
    environment:
      - WEB_DIR=${PWD}/public
      - PHP_FPM=${INTERNAL_DOCKER_HOST:-host.docker.internal}:${PHP_FPM_PORT:-9983}
    command: /bin/bash -c "envsubst '$${WEB_DIR} $${PHP_FPM}' < /nginx.conf.tpl > /etc/nginx/conf.d/rogueone-front.conf && exec nginx -g 'daemon off;'"

networks:
  default:
    external:
      name: pmd-local
