<?xml version="1.0" encoding="UTF-8"?>
<!--
CAUTION: Do not modify this file unless you know what you are doing.
 Unexpected results may occur if the code is changed deliberately.
-->
<dbmodel pgmodeler-ver="1.0.5" use-changelog="false" last-position="1068,1389" last-zoom="1" max-obj-count="38"
	 default-owner="postgres"
	 layers="Default layer"
	 active-layers="0"
	 layer-name-colors="#000000"
	 layer-rect-colors="#6e6ded"
	 show-layer-names="false" show-layer-rects="false">
<role name="rogue_one"
 inherit="true"
 login="true"
 password="********">
	<comment> <![CDATA[Rogue-One application user]]> </comment>
</role>

<database name="rogue_one" encoding="UTF8" lc-collate="en_US.UTF-8" lc-ctype="en_US.UTF-8" is-template="false" allow-conns="true">
	<role name="rogue_one"/>
	<tablespace name="pg_default"/>
</database>

<schema name="public" layers="0" rect-visible="true" fill-color="#e1e1e1" sql-disabled="true">
</schema>

<schema name="rogue_one" layers="0" rect-visible="true" fill-color="#74ee6b">
	<role name="rogue_one"/>
</schema>

<schema name="rogue_one__picbi" layers="0" rect-visible="true" fill-color="#f4f6a6">
	<role name="rogue_one"/>
</schema>

<schema name="rogue_one__import" layers="0" rect-visible="true" fill-color="#3ae81e">
	<role name="rogue_one"/>
</schema>

<tag name="to fix">
	<style id="table-body" colors="#ffffff,#fcfcfc,#808080"/>
	<style id="table-ext-body" colors="#fcfcfc,#fcfcfc,#808080"/>
	<style id="table-name" colors="#000000"/>
	<style id="table-schema-name" colors="#000000"/>
	<style id="table-title" colors="#ffff0a,#ffff0a,#20486f"/>
	<style id="table-toggler-body" colors="#000000,#000000,#000000"/>
	<style id="table-toggler-btns" colors="#000000,#000000,#000000"/>
</tag>
<tag name="to improve">
	<style id="table-body" colors="#fcfcfc,#fcfcfc,#808080"/>
	<style id="table-ext-body" colors="#fcfcfc,#fcfcfc,#808080"/>
	<style id="table-name" colors="#000000"/>
	<style id="table-schema-name" colors="#000000"/>
	<style id="table-title" colors="#80ff07,#4aa5ff,#20486f"/>
	<style id="table-toggler-body" colors="#000000,#000000,#000000"/>
	<style id="table-toggler-btns" colors="#000000,#000000,#000000"/>
</tag>
<tag name="key table">
	<style id="table-body" colors="#ffffc8,#ffffc8,#808080"/>
	<style id="table-ext-body" colors="#fcfcfc,#fcfcfc,#808080"/>
	<style id="table-name" colors="#ffffff"/>
	<style id="table-schema-name" colors="#000000"/>
	<style id="table-title" colors="#4aa5ff,#4aa5ff,#400080"/>
	<style id="table-toggler-body" colors="#000000,#000000,#000000"/>
	<style id="table-toggler-btns" colors="#000000,#000000,#000000"/>
</tag>
<sequence name="editor_id_seq" cycle="false" start="1" increment="1" min-value="1" max-value="9223372036854775807" cache="1">
	<schema name="public"/>
	<role name="rogue_one"/>
</sequence>

<table name="activity_log" layers="0" collapse-mode="2" max-obj-count="7" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<tag name="key table"/>
	<position x="520" y="960"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="username" not-null="true">
		<type name="varchar" length="200"/>
	</column>
	<column name="object_id" not-null="true">
		<type name="integer" length="0"/>
	</column>
	<column name="object_name" default-value="NULL">
		<type name="varchar" length="200"/>
	</column>
	<column name="object_label" default-value="NULL">
		<type name="varchar" length="200"/>
	</column>
	<column name="timestamp" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="activity_log_pkey" type="pk-constr" table="rogue_one.activity_log">
		<columns names="id" ref-type="src-columns"/>
	</constraint>
</table>

<table name="activity_log_diff" layers="0" collapse-mode="2" max-obj-count="7" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<position x="1000" y="1040"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="key" not-null="true">
		<type name="varchar" length="250"/>
	</column>
	<column name="from">
		<type name="text" length="0"/>
	</column>
	<column name="to">
		<type name="text" length="0"/>
	</column>
	<column name="auto">
		<type name="boolean" length="0"/>
	</column>
	<constraint name="activity_log_diff_pkey" type="pk-constr" table="rogue_one.activity_log_diff">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="activity_log_id" index="1"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="activity_log_fk" index="1"/>
	</customidxs>
</table>

<table name="base" layers="0" collapse-mode="2" max-obj-count="27" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<comment> <![CDATA[to improve : category to enum]]> </comment>
	<tag name="to improve"/>
	<position x="1580" y="1360"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="label" not-null="true">
		<type name="varchar" length="100"/>
	</column>
	<column name="pcp_campaign_ref">
		<type name="integer" length="0"/>
	</column>
	<column name="main_domain" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="sending_domain" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="sender_email" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="image_domain" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="active" not-null="true" default-value="false">
		<type name="boolean" length="0"/>
	</column>
	<column name="category" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="router" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="restricted_days">
		<type name="text" length="0"/>
		<comment> <![CDATA[(DC2Type:simple_array)]]> </comment>
	</column>
	<column name="auto_creation_days">
		<type name="text" length="0"/>
		<comment> <![CDATA[(DC2Type:simple_array)]]> </comment>
	</column>
	<column name="auto_validation_days">
		<type name="text" length="0"/>
		<comment> <![CDATA[(DC2Type:simple_array)]]> </comment>
	</column>
	<column name="router_url" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="reply_to" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="consent_ref">
		<type name="integer" length="0"/>
	</column>
	<column name="shoot_hour" default-value="NULL">
		<type name="varchar" length="5"/>
	</column>
	<column name="max_emails_per_day" default-value="1">
		<type name="integer" length="0"/>
	</column>
	<column name="frequency">
		<type name="varchar" length="100"/>
		<comment> <![CDATA[ENUM [daily, business_daily, bi_weekly, weekly, bi_monthly, monthly, event]]]> </comment>
	</column>
	<column name="create_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="update_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="base_pkey" type="pk-constr" table="rogue_one.base">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="default_template_id" index="1"/>
		<object name="editor_id" index="2"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="editor_fk" index="1"/>
		<object name="template_fk" index="2"/>
	</customidxs>
</table>

<table name="business_info" layers="0" collapse-mode="2" max-obj-count="9" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<comment> <![CDATA[to fix : id usefull ?]]> </comment>
	<tag name="to improve"/>
	<position x="2780" y="760"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
		<comment> <![CDATA[note: needed by doctrine 2.1]]> </comment>
	</column>
	<column name="integrator_code" default-value="NULL">
		<type name="varchar" length="10"/>
	</column>
	<column name="commercial_code" default-value="NULL">
		<type name="varchar" length="10"/>
	</column>
	<column name="description">
		<type name="text" length="0"/>
	</column>
	<constraint name="business_info_pk" type="pk-constr" table="rogue_one.business_info">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="advertiser_society_id" index="7"/>
		<object name="agency_society_id" index="6"/>
		<object name="email_id" index="1"/>
		<object name="platform_society_id" index="5"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="business_info_uq" index="2"/>
		<object name="email_fk" index="1"/>
		<object name="society_advertiser_fk" index="4"/>
		<object name="society_agency_fk" index="3"/>
		<object name="society_platform_fk" index="0"/>
	</customidxs>
</table>

<table name="business_info_remuneration" layers="0" collapse-mode="2" max-obj-count="3" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<position x="3280" y="560"/>
	<column name="value" not-null="true">
		<type name="double precision" length="0"/>
	</column>

	<customidxs object-type="column">
		<object name="business_info_id" index="0"/>
		<object name="name" index="1"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="business_info_fk" index="0"/>
		<object name="business_info_remuneration_pk" index="1"/>
	</customidxs>
</table>

<table name="editor" layers="0" collapse-mode="2" max-obj-count="4" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<position x="1140" y="1560"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="name" not-null="true">
		<type name="varchar" length="255"/>
	</column>
	<column name="create_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="update_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="pk_editor_id" type="pk-constr" table="rogue_one.editor">
		<columns names="id" ref-type="src-columns"/>
	</constraint>
</table>

<table name="email" layers="0" collapse-mode="2" max-obj-count="12" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<tag name="key table"/>
	<position x="2400" y="1120"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="label" not-null="true">
		<type name="varchar" length="200"/>
	</column>
	<column name="status" not-null="true">
		<type name="varchar" length="20"/>
	</column>
	<column name="shoot_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="parent_email_id">
		<type name="integer" length="0"/>
	</column>
	<column name="last_sync_date">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="last_spam_check_date">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="create_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="update_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="email_pkey" type="pk-constr" table="rogue_one.email">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="base_id" index="2"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="base_fk" index="1"/>
	</customidxs>
</table>

<table name="email_bat" layers="0" collapse-mode="2" max-obj-count="8" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<position x="2020" y="820"/>
	<column name="to" default-value="NULL">
		<type name="varchar" length="200"/>
	</column>
	<column name="cc" default-value="NULL">
		<type name="varchar" length="200"/>
	</column>
	<column name="bcc" default-value="NULL">
		<type name="varchar" length="200"/>
	</column>
	<column name="subject" default-value="NULL">
		<type name="varchar" length="250"/>
	</column>
	<column name="body">
		<type name="text" length="0"/>
	</column>
	<column name="validated">
		<type name="boolean" length="0"/>
	</column>

	<customidxs object-type="column">
		<object name="email_id" index="0"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="email_bat_uq" index="1"/>
		<object name="email_fk" index="0"/>
	</customidxs>
</table>

<table name="email_check" layers="0" collapse-mode="2" max-obj-count="7" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<position x="1840" y="1040"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="errors_count">
		<type name="integer" length="0"/>
	</column>
	<column name="messages_count">
		<type name="integer" length="0"/>
	</column>
	<column name="flags">
		<type name="text" length="0"/>
		<comment> <![CDATA[(DC2Type:simple_array)]]> </comment>
	</column>
	<column name="last_auto_check_date" default-value="NULL::timestamp without time zone">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="email_check_pkey" type="pk-constr" table="rogue_one.email_check">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="email_id" index="1"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="email_check_uq" index="2"/>
		<object name="email_fk" index="1"/>
	</customidxs>
</table>

<table name="email_check_message" layers="0" collapse-mode="2" max-obj-count="6" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<comment> <![CDATA[tofix : 
- message_level = enum ?
- check_id => email_check_id]]> </comment>
	<tag name="to improve"/>
	<position x="1500" y="720"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
		<comment> <![CDATA[note: needed by doctrine 2.1]]> </comment>
	</column>
	<column name="message_source" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="message_level" default-value="NULL">
		<type name="varchar" length="25"/>
	</column>
	<column name="content">
		<type name="text" length="0"/>
	</column>
	<constraint name="email_check_message_pk" type="pk-constr" table="rogue_one.email_check_message">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="email_check_id" index="1"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="email_check_fk" index="0"/>
	</customidxs>
</table>

<table name="email_check_message_history" layers="0" collapse-mode="2" max-obj-count="6" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<comment> <![CDATA[tofix : message_level = enum ?]]> </comment>
	<tag name="to improve"/>
	<position x="1360" y="960"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
		<comment> <![CDATA[note: needed by doctrine 2.1]]> </comment>
	</column>
	<column name="message_source" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="message_level" default-value="NULL">
		<type name="varchar" length="50"/>
	</column>
	<column name="content">
		<type name="text" length="0"/>
	</column>
	<constraint name="email_check_message_history_pk" type="pk-constr" table="rogue_one.email_check_message_history">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="email_check_id" index="1"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="email_check_fk" index="0"/>
	</customidxs>
</table>

<table name="email_content" layers="0" collapse-mode="2" max-obj-count="10" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<position x="2920" y="1180"/>
	<column name="sender" default-value="NULL">
		<type name="varchar" length="200"/>
	</column>
	<column name="reply_to" default-value="NULL">
		<type name="varchar" length="200"/>
	</column>
	<column name="subject" default-value="NULL">
		<type name="varchar" length="500"/>
	</column>
	<column name="preheader" default-value="NULL">
		<type name="varchar" length="500"/>
	</column>
	<column name="html">
		<type name="text" length="0"/>
	</column>
	<column name="ranges">
		<type name="json" length="0"/>
	</column>

	<customidxs object-type="column">
		<object name="email_id" index="0"/>
		<object name="media_id" index="6"/>
		<object name="template_id" index="5"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="email_content_pk" index="0"/>
		<object name="email_content_uq" index="2"/>
		<object name="email_fk" index="1"/>
		<object name="media_fk" index="3"/>
		<object name="template_fk" index="4"/>
	</customidxs>
</table>

<table name="email_stats" layers="0" collapse-mode="2" max-obj-count="14" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<tag name="key table"/>
	<position x="500" y="400"/>
	<column name="email_id" not-null="true">
		<type name="integer" length="0"/>
		<comment> <![CDATA[stats retrieved for a specific `email_id`, which can not exist in our system !]]> </comment>
	</column>
	<column name="sent">
		<type name="integer" length="0"/>
	</column>
	<column name="rejected">
		<type name="integer" length="0"/>
	</column>
	<column name="delivered">
		<type name="integer" length="0"/>
	</column>
	<column name="click">
		<type name="integer" length="0"/>
	</column>
	<column name="unique_click">
		<type name="integer" length="0"/>
	</column>
	<column name="open">
		<type name="integer" length="0"/>
	</column>
	<column name="unique_open">
		<type name="integer" length="0"/>
	</column>
	<column name="unsub">
		<type name="integer" length="0"/>
	</column>
	<column name="invoice">
		<type name="double precision" length="0"/>
	</column>
	<column name="create_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="update_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="email_stats_pkey" type="pk-constr" table="rogue_one.email_stats">
		<columns names="email_id" ref-type="src-columns"/>
	</constraint>
</table>

<table name="email_summary" layers="0" collapse-mode="2" max-obj-count="20" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<comment> <![CDATA[note: email_id cannot be a fk of email table, as we have legacy data from pcp-manager, from which we do not have email entities.
sent_date,email_status,base_id all used for legacy data only (pcp)]]> </comment>
	<position x="960" y="520"/>
	<column name="sent_date" default-value="NULL">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="email_status" default-value="NULL">
		<type name="varchar" length="20"/>
	</column>
	<column name="base_id" default-value="NULL">
		<type name="integer" length="0"/>
	</column>
	<column name="integrator_code" default-value="NULL">
		<type name="varchar" length="10"/>
	</column>
	<column name="integrator_label" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="commercial_code" default-value="NULL">
		<type name="varchar" length="10"/>
	</column>
	<column name="commercial_label" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="commercial_email" default-value="NULL">
		<type name="varchar" length="200"/>
	</column>
	<column name="platform_code" default-value="NULL">
		<type name="integer" length="0"/>
	</column>
	<column name="platform_label" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="advertiser_code" default-value="NULL">
		<type name="integer" length="0"/>
	</column>
	<column name="advertiser_label" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="agency_code" default-value="NULL">
		<type name="integer" length="0"/>
	</column>
	<column name="agency_label" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="brand_code" default-value="NULL">
		<type name="integer" length="0"/>
	</column>
	<column name="brand_name" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>

	<customidxs object-type="column">
		<object name="email_stats_email_id" index="0"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="email_stats_fk" index="0"/>
		<object name="email_summary_uq" index="1"/>
	</customidxs>
</table>

<table name="fragment" layers="0" collapse-mode="2" max-obj-count="8" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<comment> <![CDATA[tofix : keep id column ?]]> </comment>
	<tag name="to improve"/>
	<position x="3480" y="1240"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
		<comment> <![CDATA[note: needed by doctrine 2.1]]> </comment>
	</column>
	<column name="content">
		<type name="text" length="0"/>
	</column>
	<column name="tags">
		<type name="text" length="0"/>
		<comment> <![CDATA[(DC2Type:simple_array)]]> </comment>
	</column>
	<column name="create_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="update_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="fragment_pk" type="pk-constr" table="rogue_one.fragment">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="email_id" index="1"/>
		<object name="name" index="2"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="email_content_fk" index="1"/>
	</customidxs>
</table>

<table name="media" layers="0" collapse-mode="2" max-obj-count="8" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<tag name="key table"/>
	<position x="3460" y="1460"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="shared">
		<type name="boolean" length="0"/>
	</column>
	<column name="used">
		<type name="boolean" length="0"/>
	</column>
	<column name="directory" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="total_size">
		<type name="integer" length="0"/>
	</column>
	<column name="create_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="update_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="media_pkey" type="pk-constr" table="rogue_one.media">
		<columns names="id" ref-type="src-columns"/>
	</constraint>
</table>

<table name="media_image" layers="0" collapse-mode="2" max-obj-count="10" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<position x="3880" y="1460"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="original_image_id">
		<type name="integer" length="0"/>
	</column>
	<column name="filename" not-null="true">
		<type name="varchar" length="200"/>
	</column>
	<column name="format" default-value="NULL">
		<type name="varchar" length="4"/>
	</column>
	<column name="size" not-null="true">
		<type name="integer" length="0"/>
	</column>
	<column name="height" not-null="true">
		<type name="integer" length="0"/>
	</column>
	<column name="width" not-null="true">
		<type name="integer" length="0"/>
	</column>
	<column name="used">
		<type name="boolean" length="0"/>
	</column>
	<constraint name="media_image_pkey" type="pk-constr" table="rogue_one.media_image">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="media_id" index="2"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="media_fk" index="2"/>
	</customidxs>
</table>

<table name="planning" layers="0" collapse-mode="2" max-obj-count="4" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<tag name="key table"/>
	<position x="580" y="1460"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="name" not-null="true">
		<type name="varchar" length="100"/>
	</column>
	<column name="create_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="update_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="planning_pkey" type="pk-constr" table="rogue_one.planning">
		<columns names="id" ref-type="src-columns"/>
	</constraint>
</table>

<table name="plannings_bases" layers="0" collapse-mode="2" max-obj-count="3" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<position x="1080" y="1400"/>

	<customidxs object-type="column">
		<object name="base_id" index="1"/>
		<object name="planning_id" index="0"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="base_fk" index="2"/>
		<object name="planning_fk" index="0"/>
		<object name="plannings_bases_pk" index="1"/>
	</customidxs>
</table>

<table name="revenue_entry" layers="0" collapse-mode="2" max-obj-count="3" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<position x="1520" y="480"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="month" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="revenue_entry_pkey" type="pk-constr" table="rogue_one.revenue_entry">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="stats_revenue_id" index="1"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="stats_revenue_fk" index="1"/>
	</customidxs>
</table>

<table name="revenue_entry_detail" layers="0" collapse-mode="2" max-obj-count="4" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<position x="1960" y="440"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="name" not-null="true">
		<type name="varchar" length="10"/>
	</column>
	<column name="quantity">
		<type name="integer" length="0"/>
	</column>
	<constraint name="revenue_entry_detail_pkey" type="pk-constr" table="rogue_one.revenue_entry_detail">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="revenue_entry_id" index="1"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="revenue_entry_fk" index="1"/>
	</customidxs>
</table>

<table name="revenue_price" layers="0" collapse-mode="2" max-obj-count="4" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<position x="1500" y="320"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="name" not-null="true">
		<type name="varchar" length="10"/>
	</column>
	<column name="value" not-null="true">
		<type name="double precision" length="0"/>
	</column>
	<constraint name="revenue_price_pkey" type="pk-constr" table="rogue_one.revenue_price">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="stats_revenue_id" index="1"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="stats_revenue_fk" index="1"/>
	</customidxs>
</table>

<table name="society" layers="0" collapse-mode="2" max-obj-count="8" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<comment> <![CDATA[to improve : transform this entity to a attribute-value table (without id pk)]]> </comment>
	<position x="3260" y="900"/>
	<column name="id" not-null="true">
		<type name="integer" length="0"/>
	</column>
	<column name="contact_code">
		<type name="integer" length="0"/>
	</column>
	<column name="brand_code">
		<type name="integer" length="0"/>
	</column>
	<column name="code" not-null="true">
		<type name="integer" length="0"/>
	</column>
	<column name="name" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="kantar_code">
		<type name="integer" length="0"/>
	</column>
	<column name="kantar_label" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<constraint name="society_pkey" type="pk-constr" table="rogue_one.society">
		<columns names="id" ref-type="src-columns"/>
	</constraint>
</table>

<table name="stats_revenue" layers="0" collapse-mode="2" max-obj-count="7" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<position x="960" y="320"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="total_revenue">
		<type name="double precision" length="0"/>
	</column>
	<column name="note">
		<type name="text" length="0"/>
	</column>
	<column name="closed">
		<type name="boolean" length="0"/>
	</column>
	<column name="last_update">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="stats_revenue_pkey" type="pk-constr" table="rogue_one.stats_revenue">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="email_stats_email_id" index="1"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="email_stats_fk" index="1"/>
	</customidxs>
</table>

<table name="template" layers="0" collapse-mode="2" max-obj-count="9" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<tag name="key table"/>
	<position x="2420" y="1620"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="name" not-null="true">
		<type name="varchar" length="100"/>
	</column>
	<column name="content">
		<type name="text" length="0"/>
	</column>
	<column name="used" not-null="true" default-value="false">
		<type name="boolean" length="0"/>
		<comment> <![CDATA[if template is used by any email]]> </comment>
	</column>
	<column name="restricted" default-value="false">
		<type name="boolean" length="0"/>
		<comment> <![CDATA[if template is restricted to be used by certain shoot bases]]> </comment>
	</column>
	<column name="create_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="update_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="tags">
		<type name="text" length="0"/>
		<comment> <![CDATA[(DC2Type:simple_array)]]> </comment>
	</column>
	<constraint name="template_pkey" type="pk-constr" table="rogue_one.template">
		<columns names="id" ref-type="src-columns"/>
	</constraint>
</table>

<index name="template_name_uq" table="rogue_one.template"
	 concurrent="false" unique="true" fast-update="false" buffering="false"
	 index-type="btree" factor="90">
		<idxelement use-sorting="false">
			<column name="name"/>
		</idxelement>
</index>

<index name="planning_name_uq" table="rogue_one.planning"
	 concurrent="false" unique="true" fast-update="false" buffering="false"
	 index-type="btree" factor="90">
		<idxelement use-sorting="false">
			<column name="name"/>
		</idxelement>
</index>

<index name="base_label_uq" table="rogue_one.base"
	 concurrent="false" unique="true" fast-update="false" buffering="false"
	 index-type="btree" factor="90">
		<idxelement use-sorting="false">
			<column name="label"/>
		</idxelement>
</index>

<relationship name="email_content_has_one_email" type="rel11" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#ec1494"
	 src-table="rogue_one.email"
	 dst-table="rogue_one.email_content"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE">
	<special-pk-cols indexes="0"/>
</relationship>

<relationship name="email_content_has_many_fragment" type="rel1n" layers="0"
	 src-col-pattern="{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#918bdc"
	 src-table="rogue_one.email_content"
	 dst-table="rogue_one.fragment"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE">
	<column name="name" not-null="true">
		<type name="varchar" length="100"/>
	</column>
</relationship>

<relationship name="email_bat_has_one_email" type="rel11" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#9c94a8"
	 src-table="rogue_one.email"
	 dst-table="rogue_one.email_bat"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE"/>

<relationship name="email_check_has_many_email_check_message" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#b93488"
	 src-table="rogue_one.email_check"
	 dst-table="rogue_one.email_check_message"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE"/>

<relationship name="email_check_has_many_email_check_message_history" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#9a8141"
	 src-table="rogue_one.email_check"
	 dst-table="rogue_one.email_check_message_history"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE"/>

<relationship name="email_check_has_one_email" type="rel11" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#6bab93"
	 src-table="rogue_one.email"
	 dst-table="rogue_one.email_check"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE"/>

<relationship name="business_info_has_one_email" type="rel11" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#4968fa"
	 src-table="rogue_one.email"
	 dst-table="rogue_one.business_info"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE"/>

<table name="picbi_actor" layers="0" collapse-mode="2" max-obj-count="7" z-value="0">
	<schema name="rogue_one__picbi"/>
	<role name="rogue_one"/>
	<position x="2760" y="1900"/>
	<column name="code" not-null="true">
		<type name="varchar" length="10"/>
	</column>
	<column name="lastname" default-value="NULL">
		<type name="varchar" length="50"/>
	</column>
	<column name="firstname" default-value="NULL">
		<type name="varchar" length="50"/>
	</column>
	<column name="email" default-value="NULL">
		<type name="varchar" length="200"/>
	</column>
	<column name="create_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="update_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="picbi_actor_pkey" type="pk-constr" table="rogue_one__picbi.picbi_actor">
		<columns names="code" ref-type="src-columns"/>
	</constraint>
</table>

<table name="picbi_brand" layers="0" collapse-mode="2" max-obj-count="6" z-value="0">
	<schema name="rogue_one__picbi"/>
	<role name="rogue_one"/>
	<position x="2440" y="1900"/>
	<column name="code" not-null="true">
		<type name="integer" length="0"/>
	</column>
	<column name="name" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="sub_kantar_code">
		<type name="integer" length="0"/>
	</column>
	<column name="sub_kantar_label" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<constraint name="picbi_brand_pkey" type="pk-constr" table="rogue_one__picbi.picbi_brand">
		<columns names="code" ref-type="src-columns"/>
	</constraint>
</table>

<table name="picbi_contact" layers="0" collapse-mode="2" max-obj-count="6" z-value="0">
	<schema name="rogue_one__picbi"/>
	<role name="rogue_one"/>
	<position x="1560" y="1900"/>
	<column name="code" not-null="true">
		<type name="integer" length="0"/>
	</column>
	<column name="lastname" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="firstname" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="mail" default-value="NULL">
		<type name="varchar" length="250"/>
	</column>
	<constraint name="picbi_contact_pkey" type="pk-constr" table="rogue_one__picbi.picbi_contact">
		<columns names="code" ref-type="src-columns"/>
	</constraint>
</table>

<table name="picbi_society" layers="0" collapse-mode="2" max-obj-count="9" z-value="0">
	<schema name="rogue_one__picbi"/>
	<role name="rogue_one"/>
	<position x="1980" y="1900"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="name" not-null="true">
		<type name="varchar" length="100"/>
	</column>
	<column name="type">
		<type name="varchar" length="10"/>
	</column>
	<column name="code" not-null="true">
		<type name="integer" length="0"/>
	</column>
	<column name="kantar_code">
		<type name="integer" length="0"/>
	</column>
	<column name="kantar_label" default-value="NULL">
		<type name="varchar" length="100"/>
	</column>
	<column name="create_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="update_date" not-null="true">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="picbi_society_pkey" type="pk-constr" table="rogue_one__picbi.picbi_society">
		<columns names="id" ref-type="src-columns"/>
	</constraint>
</table>

<relationship name="base_has_many_email" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#2b9e9d"
	 src-table="rogue_one.base"
	 dst-table="rogue_one.email"
	 src-required="true" dst-required="false"
	upd-action="RESTRICT"
	del-action="RESTRICT">
	<label ref-type="name-label">
		<position x="0" y="0"/>
	</label>
</relationship>

<relationship name="society_has_many_business_info_platform" type="rel1n" layers="0"
	 src-col-pattern="platform_society_id"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_platform_fk"
	 custom-color="#92b473"
	 src-table="rogue_one.society"
	 dst-table="rogue_one.business_info"
	 src-required="false" dst-required="false"
	upd-action="CASCADE"
	del-action="SET NULL">
	<label ref-type="name-label">
		<position x="0" y="0"/>
	</label>
</relationship>

<relationship name="society_has_many_business_info_agency" type="rel1n" layers="0"
	 src-col-pattern="agency_{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_agency_fk"
	 custom-color="#4675ae"
	 src-table="rogue_one.society"
	 dst-table="rogue_one.business_info"
	 src-required="false" dst-required="false"
	upd-action="CASCADE"
	del-action="SET NULL"/>

<relationship name="society_has_many_business_info_advertiser" type="rel1n" layers="0"
	 src-col-pattern="advertiser_{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_advertiser_fk"
	 custom-color="#981f0f"
	 src-table="rogue_one.society"
	 dst-table="rogue_one.business_info"
	 src-required="false" dst-required="false"
	upd-action="CASCADE"
	del-action="SET NULL">
	<label ref-type="name-label">
		<position x="0" y="0"/>
	</label>
</relationship>

<relationship name="email_summary_has_one_email_stats" type="rel11" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#a486da"
	 src-table="rogue_one.email_stats"
	 dst-table="rogue_one.email_summary"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE"/>

<relationship name="editor_has_many_base" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#47afaa"
	 src-table="rogue_one.editor"
	 dst-table="rogue_one.base"
	 src-required="false" dst-required="false"
	upd-action="RESTRICT"
	del-action="RESTRICT"/>

<relationship name="media_has_many_email_content" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#104164"
	 src-table="rogue_one.media"
	 dst-table="rogue_one.email_content"
	 src-required="false" dst-required="false"
	upd-action="CASCADE"
	del-action="SET NULL"/>

<relationship name="media_has_many_media_image" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#304d48"
	 src-table="rogue_one.media"
	 dst-table="rogue_one.media_image"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE"/>

<function name="delete_is_forbidden"
		window-func="false"
		returns-setof="false"
		behavior-type="CALLED ON NULL INPUT"
		function-type="VOLATILE"
		security-type="SECURITY INVOKER"
		parallel-type="PARALLEL UNSAFE"
		execution-cost="1"
		row-amount="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<language name="plpgsql"/>
	<return-type>
	<type name="trigger" length="0"/>
	</return-type>
	<definition> <![CDATA[RAISE EXCEPTION "Cannot delete entities in table %", TG_TABLE_NAME;]]> </definition>
</function>

<trigger name="cannot_delete_trig" firing-type="BEFORE" per-line="false" constraint="false"
	 ins-event="false" del-event="true" upd-event="false" trunc-event="true"
	 table="rogue_one.email">
		<function signature="rogue_one.delete_is_forbidden()"/>
</trigger>

<relationship name="email_stats_has_many_stats_revenue" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#1689b2"
	 src-table="rogue_one.email_stats"
	 dst-table="rogue_one.stats_revenue"
	 src-required="true" dst-required="false"
	upd-action="RESTRICT"
	del-action="RESTRICT"/>

<trigger name="cannot_delete" firing-type="BEFORE" per-line="false" constraint="false"
	 ins-event="false" del-event="true" upd-event="false" trunc-event="true"
	 table="rogue_one.email_stats">
		<function signature="rogue_one.delete_is_forbidden()"/>
</trigger>

<relationship name="stats_revenue_has_many_revenue_price" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#e767df"
	 src-table="rogue_one.stats_revenue"
	 dst-table="rogue_one.revenue_price"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE"/>

<relationship name="stats_revenue_has_many_revenue_entry" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#38b3e0"
	 src-table="rogue_one.stats_revenue"
	 dst-table="rogue_one.revenue_entry"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE"/>

<relationship name="revenue_entry_has_many_revenue_entry_detail" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#cfd3c0"
	 src-table="rogue_one.revenue_entry"
	 dst-table="rogue_one.revenue_entry_detail"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE"/>

<relationship name="planning_has_many_plannings_bases" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#7a7b6f"
	 src-table="rogue_one.planning"
	 dst-table="rogue_one.plannings_bases"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE">
	<special-pk-cols indexes="0"/>
</relationship>

<relationship name="base_has_many_plannings_bases" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#4185bd"
	 src-table="rogue_one.base"
	 dst-table="rogue_one.plannings_bases"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE">
	<constraint name="plannings_bases_pk" type="pk-constr" alias="plannings_bases_pk" protected="true" table="rogue_one.plannings_bases">
		</constraint>
	<special-pk-cols indexes="0"/>
</relationship>

<relationship name="template_has_many_email_content" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#b23477"
	 src-table="rogue_one.template"
	 dst-table="rogue_one.email_content"
	 src-required="false" dst-required="false"
	upd-action="CASCADE"
	del-action="SET NULL"/>

<relationship name="template_has_many_base" type="rel1n" layers="0"
	 src-col-pattern="default_{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#4ac049"
	 src-table="rogue_one.template"
	 dst-table="rogue_one.base"
	 src-required="false" dst-required="false"
	upd-action="CASCADE"
	del-action="SET NULL"/>

<relationship name="activity_log_has_many_activity_log_diff" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#945c3d"
	 src-table="rogue_one.activity_log"
	 dst-table="rogue_one.activity_log_diff"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE"/>

<relationship name="picbi_society_has_many_picbi_contact" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#bb668c"
	 src-table="rogue_one__picbi.picbi_society"
	 dst-table="rogue_one__picbi.picbi_contact"
	 src-required="false" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE"/>

<relationship name="picbi_society_has_many_picbi_brand" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#0d46da"
	 src-table="rogue_one__picbi.picbi_society"
	 dst-table="rogue_one__picbi.picbi_brand"
	 src-required="false" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE"/>

<relationship name="business_info_has_many_business_info_remuneration" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#6f9f80"
	 src-table="rogue_one.business_info"
	 dst-table="rogue_one.business_info_remuneration"
	 src-required="true" dst-required="false"
	upd-action="CASCADE"
	del-action="CASCADE">
	<column name="name" not-null="true">
		<type name="varchar" length="10"/>
	</column>
	<special-pk-cols indexes="0,0"/>
</relationship>

<table name="splio_stats" layers="0" collapse-mode="2" max-obj-count="8" z-value="0">
	<schema name="rogue_one"/>
	<role name="rogue_one"/>
	<position x="960" y="800"/>
	<column name="id" not-null="true">
		<type name="bigserial" length="0"/>
	</column>
	<column name="sent">
		<type name="integer" length="0"/>
	</column>
	<column name="delivered">
		<type name="integer" length="0"/>
	</column>
	<column name="rejected">
		<type name="integer" length="0"/>
	</column>
	<column name="log_date">
		<type name="timestamptz" length="0"/>
	</column>
	<column name="log_id">
		<type name="varchar" length="10"/>
	</column>
	<constraint name="splio_stats_pk" type="pk-constr" table="rogue_one.splio_stats">
		<columns names="id" ref-type="src-columns"/>
	</constraint>

	<customidxs object-type="column">
		<object name="email_stats_email_id" index="1"/>
	</customidxs>

	<customidxs object-type="constraint">
		<object name="email_stats_fk" index="1"/>
	</customidxs>
</table>

<relationship name="email_stats_has_many_splio_stats" type="rel1n" layers="0"
	 src-col-pattern="{st}_{sc}"
	 pk-pattern="{dt}_pk" uq-pattern="{dt}_uq"
	 src-fk-pattern="{st}_fk"
	 custom-color="#66181a"
	 src-table="rogue_one.email_stats"
	 dst-table="rogue_one.splio_stats"
	 src-required="false" dst-required="false"
	upd-action="NO ACTION"
	del-action="NO ACTION"/>

<table name="user" layers="0" collapse-mode="2" max-obj-count="7" z-value="0">
	<schema name="rogue_one"/>
	<role name="postgres"/>
	<position x="2960" y="1480"/>
	<column name="id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="name">
		<type name="varchar" length="100"/>
	</column>
	<column name="login" not-null="true">
		<type name="varchar" length="250"/>
	</column>
	<column name="email">
		<type name="varchar" length="250"/>
	</column>
	<column name="password">
		<type name="varchar" length="32"/>
	</column>
	<column name="last_login_at">
		<type name="timestamptz" length="0"/>
	</column>
	<constraint name="user_pkey" type="pk-constr" table="rogue_one.&quot;user&quot;">
		<columns names="id" ref-type="src-columns"/>
	</constraint>
</table>

<table name="email_stats" layers="0" collapse-mode="2" max-obj-count="3" z-value="0">
	<schema name="rogue_one__import"/>
	<role name="rogue_one"/>
	<position x="240" y="420"/>
	<column name="email_id" not-null="true">
		<type name="serial" length="0"/>
	</column>
	<column name="unique_open">
		<type name="integer" length="0"/>
	</column>
	<column name="unique_click">
		<type name="integer" length="0"/>
	</column>
	<constraint name="email_stats_pk" type="pk-constr" table="rogue_one__import.email_stats">
		<columns names="email_id" ref-type="src-columns"/>
	</constraint>
</table>

<constraint name="parent_email_id_fk" type="fk-constr" comparison-type="MATCH FULL"
	 upd-action="NO ACTION" del-action="NO ACTION" ref-table="rogue_one.email" table="rogue_one.email">
	<columns names="parent_email_id" ref-type="src-columns"/>
	<columns names="id" ref-type="dst-columns"/>
</constraint>

<constraint name="original_image_fk" type="fk-constr" comparison-type="MATCH SIMPLE"
	 upd-action="NO ACTION" del-action="NO ACTION" ref-table="rogue_one.media_image" table="rogue_one.media_image">
	<columns names="original_image_id" ref-type="src-columns"/>
	<columns names="id" ref-type="dst-columns"/>
</constraint>

<relationship name="rel_email_email" type="relfk" layers="0"
	 src-table="rogue_one.email"
	 dst-table="rogue_one.email" reference-fk="parent_email_id_fk"
	 src-required="false" dst-required="false">
	<line>
		<position x="2706.44" y="1203.77"/>
	<position x="2706.44" y="1087"/>
	<position x="2582.29" y="1087"/>
	</line>
</relationship>

<relationship name="rel_media_image_media_image" type="relfk" layers="0"
	 src-table="rogue_one.media_image"
	 dst-table="rogue_one.media_image" reference-fk="original_image_fk"
	 src-required="false" dst-required="false">
	<line>
		<position x="4157.04" y="1534.04"/>
	<position x="4157.04" y="1427"/>
	<position x="4042.69" y="1427"/>
	</line>
</relationship>

</dbmodel>
