<?xml version="1.0"?>
<ruleset name="PcpStandard">
    <description>PCP coding standard.</description>
    <!-- Include the whole PSR2 standard -->
    <rule ref="PSR2"/>

    <!-- Disallow <? ... ?> -->
    <rule ref="Generic.PHP.DisallowShortOpenTag"/>
    <!-- Verifies that a @throws tag exists for a function that throws exceptions. -->
    <rule ref="Squiz.Commenting.FunctionCommentThrowTag" />
    <!-- Checks to ensure that there are no comments after statements. -->
    <rule ref="Squiz.Commenting.PostStatementComment" />
    <!-- Checks for empty Catch clause. Catch clause must at least have comment -->
    <rule ref="Squiz.Commenting.EmptyCatchComment" />
    <!-- Tests that the stars in a doc comment align correctly. -->
    <rule ref="Squiz.Commenting.DocCommentAlignment" />
    <!-- Disallow tabs for indent -->
    <rule ref="Generic.WhiteSpace.DisallowTabIndent"/>
    <!-- check for unused parameters -->
    <rule ref="Generic.CodeAnalysis.UnusedFunctionParameter"/>
    <!-- <rule ref="Generic.ControlStructures.InlineControlStructure"/> -->

    <!-- Line length restriction -->
    <rule ref="Generic.Files.LineLength">
        <properties>
            <property name="lineLimit" value="110"/>
            <property name="absoluteLineLimit" value="140"/>
        </properties>
    </rule>

    <!-- Only allow Unix-style line-endings -->
    <rule ref="Generic.Files.LineEndings">
        <properties>
            <property name="eolChar" value="\n"/>
        </properties>
    </rule>

    <!-- Parses and verifies the doc comments for functions. -->
    <rule ref="Squiz.Commenting.FunctionComment" />
    <!-- Disable rules bellow -->
    <rule ref="Squiz.Commenting.FunctionComment.MissingShort"><severity>0</severity></rule>
    <!-- <rule ref="Squiz.Commenting.FunctionComment.SpacingBetween"><severity>0</severity></rule> -->
    <rule ref="Squiz.Commenting.FunctionComment.SpacingAfterLongType"><severity>0</severity></rule>
    <rule ref="Squiz.Commenting.FunctionComment.SpacingBeforeParamType"><severity>0</severity></rule>
    <rule ref="Squiz.Commenting.FunctionComment.SpacingAfterParams"><severity>0</severity></rule>
    <rule ref="Squiz.Commenting.FunctionComment.MissingParamComment"><severity>0</severity></rule>
    <rule ref="Squiz.Commenting.FunctionComment.EmptyThrows"><severity>0</severity></rule>

    <!-- Parses and verifies the variable doc comment. -->
    <rule ref="Squiz.Commenting.VariableComment" />
    <!-- Disable rules bellow -->
    <rule ref="Squiz.Commenting.VariableComment.MissingShort"><severity>0</severity></rule>
    <rule ref="Squiz.Commenting.VariableComment.SpacingBeforeTags"><severity>0</severity></rule>
    <!-- <rule ref="Squiz.Commenting.VariableComment.ShortSingleLine"><severity>0</severity></rule> -->

    <!-- Other usefull but not used yet -->
    <!-- Parses and verifies the class doc comment. -->
    <!-- <rule ref="Squiz.Commenting.ClassComment" /> -->
    <!-- Parses and verifies the file doc comment. -->
    <!-- <rule ref="Squiz.Commenting.FileComment" /> -->
</ruleset>