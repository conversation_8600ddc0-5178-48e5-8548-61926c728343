#!/bin/bash

# This file is subject to the terms and conditions defined in file
# 'LICENSE.txt', which is part of this source code package.
# 
# @copyright Prisma Group (C) 2014-2015
# @license Close source - see LICENSE.txt file


# WARNING : This script assume each file processed has
# already a file comment
usage() {
    printf 'Usage: %s <directory>\n' $0
    exit 1
}

DIR="${1:-}"
TMP_DIR="/tmp"

[ $# -ne 1 ] && usage
[ ! -d "${DIR}" ] && usage

##############################################################################
# MAKE THE STUFF
##############################################################################
printf '\n'
printf '\033[1;32mProcess *.php files in : %s\033[0m\n\n' "${DIR}"
printf '\n'
for file in $( find "${DIR}" -name "*.php" | grep -v 'config/' ); do
    tmp_file="${TMP_DIR}/$( basename "${file}" ).tmp"
    # get first ' */'
    pos_start=$( grep -m 1 -n '/[*][*]' < "${file}" | cut -d':' -f1)
    pos_end=$( grep -m 1 -n ' [*]/' < "${file}" | cut -d':' -f1)

    if [ $pos_start -gt 2 ]; then
        printf '. \33[1;37;41m Err \033[0m\n - %s\n' "${file}"
        continue;
    fi

    printf '<?php\n'                      >  "${tmp_file}"
    tail -n +$(( pos_end + 1 )) "${file}" >> "${tmp_file}"
    mv "${tmp_file}" "${file}"

    printf '. \033[1;32m Done \033[0m - %s\n' "${file#${DIR}}"
done

printf '\n'
printf '\033[1;32mFinish\033[0m\n'
printf '\n'