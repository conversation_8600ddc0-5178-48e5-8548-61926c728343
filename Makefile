CURRENT_DIR = $(shell pwd)
PHP_CONTAINER=docker exec -it -w $(CURRENT_DIR) pmd-local-php-fpm-8-3
PHP_BIN = $(PHP_CONTAINER) php
PHPCSFIXER = $(PHP_BIN) ./vendor/bin/php-cs-fixer

start:
	docker-compose --env-file ./.env.local up -d
	@echo '✅ Running on https://local.rogue-one.prismadata.fr/'

stop:
	docker-compose stop

rector:
	$(PHP_BIN) ./vendor/bin/rector process -vvvv --config=rector.php --autoload-file vendor/autoload.php

composer:
	$(PHP_CONTAINER) composer install --ignore-platform-req=ext-mongodb --ignore-platform-req=ext-grpc

composer-update:
	$(PHP_CONTAINER) composer update --ignore-platform-req=ext-mongodb --ignore-platform-req=ext-grpc --ignore-platform-req=ext-http

cs-fix:
	$(PHPCSFIXER) fix --config=./.php-cs-fixer.php --verbose --show-progress=dots

cs-check:
	$(PHPCSFIXER) fix --config=./.php-cs-fixer.php src --dry-run --stop-on-violation --using-cache=no

restart-redis:
	cd $(DOCKER_DATA_DIR) && make stop-redis && make start-redis

bash:
	$(PHP_CONTAINER) bash

start-services:
	cd $(DOCKER_DATA_DIR) && make start-traefik && make start-redis

generate-proxies:
	$(PHP_BIN) ./vendor/bin/doctrine-module orm:generate-proxies
