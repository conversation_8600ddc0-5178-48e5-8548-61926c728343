# Docker PHP
LOCAL_ADDRESS=host.docker.internal
PHP_FPM_PORT=9981

# Local PHP
#LOCAL_ADDRESS=127.0.0.1
#PHP_FPM_PORT=9081

APP_ENV=dev
APP_SECRET=NotSecret
PSQL_DB=matrix
PSQL_PASSWORD=''
PSQL_PORT=5432
DOCKER_DATA_DIR=~/workspace/docker-data/

PSQL_SOCKET=${LOCAL_ADDRESS}
PSQL_VERSION=14.12.1
PSQL_USER=rogue_one_app
OAUTH_GOOGLE_CLIENT_ID=
OAUTH_GOOGLE_CLIENT_SECRET=
MATRIX_PROJECT_ID=pm-preprod-matrix

#GCS
CLOUD_STORAGE_PROJECT=

# Not used in local
GOOGLE_PROJECT_ID=rogue-one
NLF_GET_NL_URL='https://nl-factory.preprod.prismadata.fr/get-nl/'
ROOT_DOMAIN='local.rogue-one.prismadata.fr'
