server {
    index index.php index.html;
    server_name local.rogue-one.prismadata.fr rogue-one.local.com;
    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
    root ${WEB_DIR};

    location / {
        try_files $uri $uri/ /index.php?$args;
    }

    client_max_body_size 5m;
    try_files $uri @app;

    location @app {
        add_header Access-Control-Allow-Origin *;
        # Redirect url with ending slashes
        rewrite ^(.*)$ /index.php$1 last;
    }

    location ~ \.php$ {
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass ${PHP_FPM};
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;

        add_header X-Content-Type-Options nosniff;
        add_header Content-Security-Policy "upgrade-insecure-requests; connect-src * https:; img-src * data: https:; font-src * data:; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline'; media-src * blob: data:; worker-src * blob: data:";
        add_header Strict-Transport-Security 'max-age=63072000; includeSubDomains; preload; always;';
    }

    location ~ \.(?:css|js|ico|jpe?g|png|gif|woff2?|eot|svg|ttf|otf)$ {
        expires 1y;
        add_header Cache-Control public;
        add_header Access-Control-Allow-Origin *;

        add_header X-Content-Type-Options nosniff;
        add_header Content-Security-Policy "upgrade-insecure-requests; connect-src * https:; img-src * data: https:; font-src * data:; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline'; media-src * blob: data:; worker-src * blob: data:";
        add_header Strict-Transport-Security 'max-age=63072000; includeSubDomains; preload; always;';
    }
}
