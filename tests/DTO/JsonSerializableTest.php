<?php

namespace App\Tests\DTO;

use App\DTO\PicbiBrandDTO;
use App\DTO\PicbiContactDTO;
use App\DTO\PicbiSocietyDTO;
use PHPUnit\Framework\TestCase;

class JsonSerializableTest extends TestCase
{
    public function testJsonEncodeWorksDirectlyWithDTOs(): void
    {
        $contactDto = new PicbiContactDTO(
            code: 123456,
            lastname: '<PERSON><PERSON>',
            firstname: '<PERSON>',
            mail: '<EMAIL>',
            fullName: '<PERSON>',
            fullContact: '<PERSON> <<EMAIL>>'
        );

        $brandDto = new PicbiBrandDTO(
            code: 789012,
            name: 'Test Brand',
            subKantarCode: 330,
            subKantarLabel: 'INFORMATION MEDIA'
        );

        $societyDto = new PicbiSocietyDTO(
            name: 'Test Society',
            type: 'AB',
            code: 12345,
            contacts: [$contactDto],
            brands: [$brandDto],
            kantarCode: 33,
            kantarLabel: 'INFORMATION MEDIA',
            id: 1,
            createDate: '2020-02-06T12:23:18+00:00',
            updateDate: '2020-02-06T12:23:18+00:00'
        );

        // Test that json_encode works directly with the DTO
        $json = json_encode($societyDto);
        $this->assertIsString($json);
        
        $decoded = json_decode($json, true);
        $this->assertIsArray($decoded);
        
        // Verify the structure
        $this->assertEquals('Test Society', $decoded['name']);
        $this->assertEquals('AB', $decoded['type']);
        $this->assertEquals(12345, $decoded['code']);
        $this->assertIsArray($decoded['contacts']);
        $this->assertIsArray($decoded['brands']);
        
        // Verify nested objects are also properly serialized
        $this->assertEquals(123456, $decoded['contacts'][0]['code']);
        $this->assertEquals('John Doe', $decoded['contacts'][0]['full_name']);
        $this->assertEquals(789012, $decoded['brands'][0]['code']);
        $this->assertEquals('Test Brand', $decoded['brands'][0]['name']);
    }

    public function testJsonEncodeWithArrayOfDTOs(): void
    {
        $dto1 = new PicbiSocietyDTO(
            name: 'Society 1',
            type: 'AB',
            code: 111,
            contacts: [],
            brands: [],
            kantarCode: null,
            kantarLabel: null,
            id: 1,
            createDate: null,
            updateDate: null
        );

        $dto2 = new PicbiSocietyDTO(
            name: 'Society 2',
            type: 'AG',
            code: 222,
            contacts: [],
            brands: [],
            kantarCode: null,
            kantarLabel: null,
            id: 2,
            createDate: null,
            updateDate: null
        );

        // Test that json_encode works with arrays of DTOs
        $json = json_encode([$dto1, $dto2]);
        $this->assertIsString($json);
        
        $decoded = json_decode($json, true);
        $this->assertIsArray($decoded);
        $this->assertCount(2, $decoded);
        
        $this->assertEquals('Society 1', $decoded[0]['name']);
        $this->assertEquals('Society 2', $decoded[1]['name']);
        $this->assertEquals(111, $decoded[0]['code']);
        $this->assertEquals(222, $decoded[1]['code']);
    }
}
