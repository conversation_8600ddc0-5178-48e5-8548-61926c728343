<?php

namespace App\Tests\DTO;

use App\DTO\PicbiContactDTO;
use App\Entity\PicbiContact;
use PHPUnit\Framework\TestCase;

class PicbiContactDTOTest extends TestCase
{
    public function testFromEntity(): void
    {
        $contact = new PicbiContact();
        $contact->setCode(123456);
        $contact->setFirstname('<PERSON>');
        $contact->setLastname('Doe');
        $contact->setMail('<EMAIL>');

        $dto = PicbiContactDTO::fromEntity($contact);

        $this->assertEquals(123456, $dto->code);
        $this->assertEquals('Doe', $dto->lastname);
        $this->assertEquals('John', $dto->firstname);
        $this->assertEquals('<EMAIL>', $dto->mail);
        $this->assertEquals('<PERSON>', $dto->fullName);
        $this->assertEquals('<PERSON> <<EMAIL>>', $dto->fullContact);
    }

    public function testFromEntityWithoutEmail(): void
    {
        $contact = new PicbiContact();
        $contact->setCode(123456);
        $contact->setFirstname('John');
        $contact->setLastname('Doe');
        // No email set

        $dto = PicbiContactDTO::fromEntity($contact);

        $this->assertEquals(123456, $dto->code);
        $this->assertEquals('Doe', $dto->lastname);
        $this->assertEquals('John', $dto->firstname);
        $this->assertNull($dto->mail);
        $this->assertEquals('John Doe', $dto->fullName);
        $this->assertEquals('John Doe', $dto->fullContact); // No email brackets
    }

    public function testFromEntityWithPartialName(): void
    {
        $contact = new PicbiContact();
        $contact->setCode(123456);
        $contact->setFirstname('John');
        // No lastname set
        $contact->setMail('<EMAIL>');

        $dto = PicbiContactDTO::fromEntity($contact);

        $this->assertEquals('John', $dto->fullName);
        $this->assertEquals('John <<EMAIL>>', $dto->fullContact);
    }

    public function testJsonSerialize(): void
    {
        $dto = new PicbiContactDTO(
            code: 123456,
            lastname: 'Doe',
            firstname: 'John',
            mail: '<EMAIL>',
            fullName: 'John Doe',
            fullContact: 'John Doe <<EMAIL>>'
        );

        $array = $dto->jsonSerialize();

        $this->assertEquals([
            'code' => 123456,
            'lastname' => 'Doe',
            'firstname' => 'John',
            'mail' => '<EMAIL>',
            'full_name' => 'John Doe',
            'full_contact' => 'John Doe <<EMAIL>>'
        ], $array);
    }

    public function testFromEntities(): void
    {
        $contact1 = new PicbiContact();
        $contact1->setCode(111);
        $contact1->setFirstname('John');
        $contact1->setLastname('Doe');

        $contact2 = new PicbiContact();
        $contact2->setCode(222);
        $contact2->setFirstname('Jane');
        $contact2->setLastname('Smith');

        $dtos = PicbiContactDTO::fromEntities([$contact1, $contact2]);

        $this->assertCount(2, $dtos);
        $this->assertInstanceOf(PicbiContactDTO::class, $dtos[0]);
        $this->assertInstanceOf(PicbiContactDTO::class, $dtos[1]);
        $this->assertEquals(111, $dtos[0]->code);
        $this->assertEquals(222, $dtos[1]->code);
        $this->assertEquals('John Doe', $dtos[0]->fullName);
        $this->assertEquals('Jane Smith', $dtos[1]->fullName);
    }
}
