<?php

namespace App\Tests\DTO;

use App\DTO\PicbiBrandDTO;
use App\DTO\PicbiContactDTO;
use App\DTO\PicbiSocietyDTO;
use App\Entity\PicbiBrand;
use App\Entity\PicbiContact;
use App\Entity\PicbiSociety;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\TestCase;

class PicbiSocietyDTOTest extends TestCase
{
    public function testFromEntity(): void
    {
        // Create mock entities
        $society = new PicbiSociety();
        $society->setName('Test Society');
        $society->setType('AB');
        $society->setCode(12345);
        $society->setKantarCode(33);
        $society->setKantarLabel('INFORMATION MEDIA');
        $society->setCreateDate(new \DateTime('2020-02-06T12:23:18+00:00'));
        $society->setUpdateDate(new \DateTime('2020-02-06T12:23:18+00:00'));

        $contact = new PicbiContact();
        $contact->setCode(123456);
        $contact->setFirstname('John');
        $contact->setLastname('Doe');
        $contact->setMail('<EMAIL>');

        $brand = new PicbiBrand();
        $brand->setCode(789012);
        $brand->setName('Test Brand');
        $brand->setSubKantarCode(330);
        $brand->setSubKantarLabel('INFORMATION MEDIA');

        // Mock collections
        $contacts = new ArrayCollection([$contact]);
        $brands = new ArrayCollection([$brand]);
        
        // Use reflection to set the collections since they're private
        $reflection = new \ReflectionClass($society);
        $contactsProperty = $reflection->getProperty('contacts');
        $contactsProperty->setAccessible(true);
        $contactsProperty->setValue($society, $contacts);
        
        $brandsProperty = $reflection->getProperty('brands');
        $brandsProperty->setAccessible(true);
        $brandsProperty->setValue($society, $brands);

        // Create DTO from entity
        $dto = PicbiSocietyDTO::fromEntity($society);

        // Assertions
        $this->assertEquals('Test Society', $dto->name);
        $this->assertEquals('AB', $dto->type);
        $this->assertEquals(12345, $dto->code);
        $this->assertEquals(33, $dto->kantarCode);
        $this->assertEquals('INFORMATION MEDIA', $dto->kantarLabel);
        $this->assertEquals('2020-02-06T12:23:18+00:00', $dto->createDate);
        $this->assertEquals('2020-02-06T12:23:18+00:00', $dto->updateDate);
        
        // Check contacts
        $this->assertCount(1, $dto->contacts);
        $contactDto = $dto->contacts[0];
        $this->assertInstanceOf(\App\DTO\PicbiContactDTO::class, $contactDto);
        $this->assertEquals(123456, $contactDto->code);
        $this->assertEquals('John', $contactDto->firstname);
        $this->assertEquals('Doe', $contactDto->lastname);
        $this->assertEquals('<EMAIL>', $contactDto->mail);
        $this->assertEquals('John Doe', $contactDto->fullName);
        $this->assertEquals('John Doe <<EMAIL>>', $contactDto->fullContact);

        // Check brands
        $this->assertCount(1, $dto->brands);
        $brandDto = $dto->brands[0];
        $this->assertInstanceOf(\App\DTO\PicbiBrandDTO::class, $brandDto);
        $this->assertEquals(789012, $brandDto->code);
        $this->assertEquals('Test Brand', $brandDto->name);
        $this->assertEquals(330, $brandDto->subKantarCode);
        $this->assertEquals('INFORMATION MEDIA', $brandDto->subKantarLabel);
    }

    public function testToArray(): void
    {
        $contactDto = new PicbiContactDTO(
            code: 123456,
            lastname: 'Doe',
            firstname: 'John',
            mail: '<EMAIL>',
            fullName: 'John Doe',
            fullContact: 'John Doe <<EMAIL>>'
        );

        $brandDto = new PicbiBrandDTO(
            code: 789012,
            name: 'Test Brand',
            subKantarCode: 330,
            subKantarLabel: 'INFORMATION MEDIA'
        );

        $dto = new PicbiSocietyDTO(
            name: 'Test Society',
            type: 'AB',
            code: 12345,
            contacts: [$contactDto],
            brands: [$brandDto],
            kantarCode: 33,
            kantarLabel: 'INFORMATION MEDIA',
            id: 1,
            createDate: '2020-02-06T12:23:18+00:00',
            updateDate: '2020-02-06T12:23:18+00:00'
        );

        $array = $dto->toArray();

        $this->assertEquals([
            'name' => 'Test Society',
            'type' => 'AB',
            'code' => 12345,
            'contacts' => [$contactDto],
            'brands' => [$brandDto],
            'kantar_code' => 33,
            'kantar_label' => 'INFORMATION MEDIA',
            'id' => 1,
            'create_date' => '2020-02-06T12:23:18+00:00',
            'update_date' => '2020-02-06T12:23:18+00:00'
        ], $array);
    }

    public function testFromEntities(): void
    {
        $society1 = new PicbiSociety();
        $society1->setName('Society 1');
        $society1->setType('AB');
        $society1->setCode(111);

        $society2 = new PicbiSociety();
        $society2->setName('Society 2');
        $society2->setType('AG');
        $society2->setCode(222);

        // Mock empty collections for both societies
        $emptyContacts = new ArrayCollection();
        $emptyBrands = new ArrayCollection();
        
        $reflection = new \ReflectionClass(PicbiSociety::class);
        $contactsProperty = $reflection->getProperty('contacts');
        $contactsProperty->setAccessible(true);
        $brandsProperty = $reflection->getProperty('brands');
        $brandsProperty->setAccessible(true);
        
        foreach ([$society1, $society2] as $society) {
            $contactsProperty->setValue($society, $emptyContacts);
            $brandsProperty->setValue($society, $emptyBrands);
        }

        $dtos = PicbiSocietyDTO::fromEntities([$society1, $society2]);

        $this->assertCount(2, $dtos);
        $this->assertInstanceOf(PicbiSocietyDTO::class, $dtos[0]);
        $this->assertInstanceOf(PicbiSocietyDTO::class, $dtos[1]);
        $this->assertEquals('Society 1', $dtos[0]->name);
        $this->assertEquals('Society 2', $dtos[1]->name);
    }

    public function testToArrays(): void
    {
        $dto1 = new PicbiSocietyDTO(
            name: 'Society 1',
            type: 'AB',
            code: 111,
            contacts: [],
            brands: [],
            kantarCode: null,
            kantarLabel: null,
            id: 1,
            createDate: null,
            updateDate: null
        );

        $dto2 = new PicbiSocietyDTO(
            name: 'Society 2',
            type: 'AG',
            code: 222,
            contacts: [],
            brands: [],
            kantarCode: null,
            kantarLabel: null,
            id: 2,
            createDate: null,
            updateDate: null
        );

        $arrays = PicbiSocietyDTO::toArrays([$dto1, $dto2]);

        $this->assertCount(2, $arrays);
        $this->assertEquals('Society 1', $arrays[0]['name']);
        $this->assertEquals('Society 2', $arrays[1]['name']);
        $this->assertEquals(111, $arrays[0]['code']);
        $this->assertEquals(222, $arrays[1]['code']);
    }

    public function testContactTransformationWithoutEmail(): void
    {
        $society = new PicbiSociety();
        $society->setName('Test Society');
        $society->setCode(12345);

        $contact = new PicbiContact();
        $contact->setCode(123456);
        $contact->setFirstname('John');
        $contact->setLastname('Doe');
        // No email set

        $contacts = new ArrayCollection([$contact]);
        $brands = new ArrayCollection();
        
        $reflection = new \ReflectionClass($society);
        $contactsProperty = $reflection->getProperty('contacts');
        $contactsProperty->setAccessible(true);
        $contactsProperty->setValue($society, $contacts);
        
        $brandsProperty = $reflection->getProperty('brands');
        $brandsProperty->setAccessible(true);
        $brandsProperty->setValue($society, $brands);

        $dto = PicbiSocietyDTO::fromEntity($society);

        $contactDto = $dto->contacts[0];
        $this->assertInstanceOf(\App\DTO\PicbiContactDTO::class, $contactDto);
        $this->assertEquals('John Doe', $contactDto->fullName);
        $this->assertEquals('John Doe', $contactDto->fullContact); // No email brackets
        $this->assertNull($contactDto->mail);
    }
}
