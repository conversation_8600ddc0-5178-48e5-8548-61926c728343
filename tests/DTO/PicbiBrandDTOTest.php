<?php

namespace App\Tests\DTO;

use App\DTO\PicbiBrandDTO;
use App\Entity\PicbiBrand;
use PHPUnit\Framework\TestCase;

class PicbiBrandDTOTest extends TestCase
{
    public function testFromEntity(): void
    {
        $brand = new PicbiBrand();
        $brand->setCode(789012);
        $brand->setName('Test Brand');
        $brand->setSubKantarCode(330);
        $brand->setSubKantarLabel('INFORMATION MEDIA');

        $dto = PicbiBrandDTO::fromEntity($brand);

        $this->assertEquals(789012, $dto->code);
        $this->assertEquals('Test Brand', $dto->name);
        $this->assertEquals(330, $dto->subKantarCode);
        $this->assertEquals('INFORMATION MEDIA', $dto->subKantarLabel);
    }

    public function testFromEntityWithNullValues(): void
    {
        $brand = new PicbiBrand();
        $brand->setCode(789012);
        $brand->setName('Test Brand');
        // subKantarCode and subKantarLabel are null

        $dto = PicbiBrandDTO::fromEntity($brand);

        $this->assertEquals(789012, $dto->code);
        $this->assertEquals('Test Brand', $dto->name);
        $this->assertNull($dto->subKantarCode);
        $this->assertNull($dto->subKantarLabel);
    }

    public function testJsonSerialize(): void
    {
        $dto = new PicbiBrandDTO(
            code: 789012,
            name: 'Test Brand',
            subKantarCode: 330,
            subKantarLabel: 'INFORMATION MEDIA'
        );

        $array = $dto->jsonSerialize();

        $this->assertEquals([
            'code' => 789012,
            'name' => 'Test Brand',
            'sub_kantar_code' => 330,
            'sub_kantar_label' => 'INFORMATION MEDIA'
        ], $array);
    }

    public function testFromEntities(): void
    {
        $brand1 = new PicbiBrand();
        $brand1->setCode(111);
        $brand1->setName('Brand 1');

        $brand2 = new PicbiBrand();
        $brand2->setCode(222);
        $brand2->setName('Brand 2');

        $dtos = PicbiBrandDTO::fromEntities([$brand1, $brand2]);

        $this->assertCount(2, $dtos);
        $this->assertInstanceOf(PicbiBrandDTO::class, $dtos[0]);
        $this->assertInstanceOf(PicbiBrandDTO::class, $dtos[1]);
        $this->assertEquals(111, $dtos[0]->code);
        $this->assertEquals(222, $dtos[1]->code);
        $this->assertEquals('Brand 1', $dtos[0]->name);
        $this->assertEquals('Brand 2', $dtos[1]->name);
    }
}
