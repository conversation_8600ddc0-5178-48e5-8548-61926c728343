<?php

namespace App\Tests\Controller\Api;

use App\Controller\Api\PicbiSocietyController;
use App\Entity\PicbiBrand;
use App\Entity\PicbiContact;
use App\Entity\PicbiSociety;
use App\Service\PicbiSocietyService;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class PicbiSocietyControllerUnitTest extends TestCase
{
    private PicbiSocietyService $mockService;
    private PicbiSocietyController $controller;

    protected function setUp(): void
    {
        $this->mockService = $this->createMock(PicbiSocietyService::class);
        $this->controller = new PicbiSocietyController($this->mockService);
    }

    public function testGetListWithValidKeyword(): void
    {
        // Create mock entities
        $society = new PicbiSociety();
        $society->setName('Test Society');
        $society->setType('AB');
        $society->setCode(12345);
        $society->setKantarCode(33);
        $society->setKantarLabel('INFORMATION MEDIA');
        $society->setCreateDate(new \DateTime('2020-02-06T12:23:18+00:00'));
        $society->setUpdateDate(new \DateTime('2020-02-06T12:23:18+00:00'));

        // Mock collections
        $contacts = new ArrayCollection();
        $brands = new ArrayCollection();
        
        // Use reflection to set the collections since they're private
        $reflection = new \ReflectionClass($society);
        $contactsProperty = $reflection->getProperty('contacts');
        $contactsProperty->setAccessible(true);
        $contactsProperty->setValue($society, $contacts);
        
        $brandsProperty = $reflection->getProperty('brands');
        $brandsProperty->setAccessible(true);
        $brandsProperty->setValue($society, $brands);

        // Mock the service response
        $this->mockService->expects($this->once())
            ->method('findFromKeywords')
            ->with(['keywords' => 'test'])
            ->willReturn([$society]);

        // Create request with JSON payload
        $request = new Request(
            [],
            [],
            [],
            [],
            [],
            ['CONTENT_TYPE' => 'application/json'],
            json_encode(['keyword' => 'test'])
        );

        // Call the controller method
        $response = $this->controller->getList($request);

        // Assertions
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        
        $this->assertEquals(200, $responseData['status']);
        $this->assertEquals('Societies found', $responseData['detail']);
        $this->assertArrayHasKey('data', $responseData['more']);
        $this->assertCount(1, $responseData['more']['data']);
        
        $societyData = $responseData['more']['data'][0];
        $this->assertEquals('Test Society', $societyData['name']);
        $this->assertEquals('AB', $societyData['type']);
        $this->assertEquals(12345, $societyData['code']);
        $this->assertEquals(33, $societyData['kantar_code']);
        $this->assertEquals('INFORMATION MEDIA', $societyData['kantar_label']);
        $this->assertIsArray($societyData['contacts']);
        $this->assertIsArray($societyData['brands']);
    }

    public function testGetListWithMissingKeyword(): void
    {
        $request = new Request(
            [],
            [],
            [],
            [],
            [],
            ['CONTENT_TYPE' => 'application/json'],
            json_encode([])
        );

        $response = $this->controller->getList($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        
        $this->assertEquals(400, $responseData['status']);
        $this->assertEquals('Keyword is required', $responseData['detail']);
    }

    public function testGetListWithInvalidJson(): void
    {
        $request = new Request(
            [],
            [],
            [],
            [],
            [],
            ['CONTENT_TYPE' => 'application/json'],
            'invalid json'
        );

        $response = $this->controller->getList($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        
        $this->assertEquals(400, $responseData['status']);
        $this->assertEquals('Invalid JSON payload', $responseData['detail']);
    }

    public function testGetListWithServiceException(): void
    {
        $this->mockService->expects($this->once())
            ->method('findFromKeywords')
            ->willThrowException(new \DomainException('Invalid type provided'));

        $request = new Request(
            [],
            [],
            [],
            [],
            [],
            ['CONTENT_TYPE' => 'application/json'],
            json_encode(['keyword' => 'test'])
        );

        $response = $this->controller->getList($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        
        $this->assertEquals(400, $responseData['status']);
        $this->assertEquals('Invalid type provided', $responseData['detail']);
    }
}
