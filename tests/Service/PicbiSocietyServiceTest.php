<?php

namespace App\Tests\Service;

use App\Entity\PicbiSociety;
use App\Repository\PicbiSocietyRepository;
use App\Service\PicbiSocietyService;
use PHPUnit\Framework\TestCase;

class PicbiSocietyServiceTest extends TestCase
{
    private PicbiSocietyRepository $repository;
    private PicbiSocietyService $service;

    protected function setUp(): void
    {
        $this->repository = $this->createMock(PicbiSocietyRepository::class);
        $this->service = new PicbiSocietyService($this->repository);
    }

    public function testFindFromKeywordsWithValidType(): void
    {
        $filter = ['type' => 'AB', 'keywords' => 'test'];
        $expectedResult = [new PicbiSociety()];

        $this->repository
            ->expects($this->once())
            ->method('findFromKeywords')
            ->with($filter)
            ->willReturn($expectedResult);

        $result = $this->service->findFromKeywords($filter);

        $this->assertSame($expectedResult, $result);
    }

    public function testFindFromKeywordsWithInvalidType(): void
    {
        $filter = ['type' => 'INVALID', 'keywords' => 'test'];

        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('App\Service\PicbiSocietyService::findFromKeywords does not support type "INVALID"');

        $this->service->findFromKeywords($filter);
    }

    public function testFindFromKeywordsWithoutType(): void
    {
        $filter = ['keywords' => 'test'];
        $expectedResult = [new PicbiSociety()];

        $this->repository
            ->expects($this->once())
            ->method('findFromKeywords')
            ->with($filter)
            ->willReturn($expectedResult);

        $result = $this->service->findFromKeywords($filter);

        $this->assertSame($expectedResult, $result);
    }

    public function testFindFromKeywordsWithEmptyFilter(): void
    {
        $filter = [];
        $expectedResult = [];

        $this->repository
            ->expects($this->once())
            ->method('findFromKeywords')
            ->with($filter)
            ->willReturn($expectedResult);

        $result = $this->service->findFromKeywords($filter);

        $this->assertSame($expectedResult, $result);
    }

    public function testAllowedTypesAreValid(): void
    {
        $allowedTypes = [
            'AB', 'AG', 'AN', 'AT', 'BP', 'CA', 'ED', 'FO', 'JU', 'MBB', 'PA', 'REP'
        ];

        $this->repository
            ->expects($this->exactly(count($allowedTypes)))
            ->method('findFromKeywords')
            ->willReturn([]);

        foreach ($allowedTypes as $type) {
            $filter = ['type' => $type];

            // This should not throw an exception
            $this->service->findFromKeywords($filter);
        }
    }
}
