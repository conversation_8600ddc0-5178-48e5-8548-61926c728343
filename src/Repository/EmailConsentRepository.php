<?php

namespace App\Repository;

use App\Entity\EmailConsent;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method EmailConsent|null find($id, $lockMode = null, $lockVersion = null)
 * @method EmailConsent|null findOneBy(array $criteria, array $orderBy = null)
 * @method EmailConsent[]    findAll()
 * @method EmailConsent[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EmailConsentRepository extends AbstractRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EmailConsent::class);
    }

    public function getConsentEntityList(): array
    {
        return $this->createQueryBuilder('c')
            ->where('c.active = true')
            ->orderBy('c.publicRef', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function getThematicsList(): array
    {
        $sql =
<<<SQL
SELECT public_ref 
FROM karinto.thematic 
WHERE active = true 
ORDER BY public_ref ASC
SQL;
        $query = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $query->executeQuery()->fetchAllAssociative();

        return array_column($result, 'public_ref');
    }

    public function getConsentList(): array
    {
        $sql =
<<<SQL
SELECT public_ref 
FROM karinto.email_consent 
WHERE active = true 
ORDER BY public_ref ASC
SQL;
        $query = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $query->executeQuery()->fetchAllAssociative();

        return array_column($result, 'public_ref');
    }
}
