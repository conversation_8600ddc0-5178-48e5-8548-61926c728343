<?php

namespace App\Repository;

use App\Entity\Template as TemplateEntity;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method TemplateEntity|null find($id, $lockMode = null, $lockVersion = null)
 * @method TemplateEntity|null findOneBy(array $criteria, array $orderBy = null)
 * @method TemplateEntity[]    findAll()
 * @method TemplateEntity[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TemplateRepository extends AbstractRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TemplateEntity::class);
    }

    public function getNLFactoryPublicUrls(): array
    {
        $sql =
<<<SQL
    SELECT nl_ref
    FROM nl_factory.public_url
    ORDER BY nl_ref
SQL;

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);
        $query = $stmt->executeQuery();
        $result = $query->fetchAllAssociative();
        $data = [];
        foreach ($result as $index => $row) {
            $data[$row['nl_ref']] = $row['nl_ref'];
        }

        return $data;
    }
}
