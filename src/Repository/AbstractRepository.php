<?php

namespace App\Repository;

use App\Entity\EntityInterface;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\UnitOfWork;
use Doctrine\Persistence\ManagerRegistry;

abstract class AbstractRepository extends ServiceEntityRepository implements RepositoryInterface
{
    public function __construct(ManagerRegistry $registry, string $entityClassName)
    {
        parent::__construct($registry, $entityClassName);
    }

    /**
     * Get an entity by its id.
     */
    public function getById($id): ?EntityInterface
    {
        return $this->find($id);
    }

    public function findOne(array $filter = [], array $sort = []): ?EntityInterface
    {
        if (!empty($sort)) {
            foreach ($sort as $field => $value) {
                $sort[$field] = is_string($value) ? $value : ($value ? 'ASC' : 'DESC');
            }
        }

        return $this->findOneBy($filter, $sort);
    }

    /**
     * @codeCoverageIgnore
     */
    public function findMany(array $filter = [], array $sort = [], ?int $limit = null, ?array $offset = null): array
    {
        if (!empty($sort)) {
            foreach ($sort as $field => $value) {
                $sort[$field] = is_string($value) ? $value : ($value ? 'ASC' : 'DESC');
            }
        }

        return $this->findBy($filter, $sort, $limit, $offset);
    }

    /**
     * Create new entity.
     */
    public function createOne(EntityInterface $entity, bool $flushOnlyEntity = false): AbstractRepository
    {
        try {
            $this->getEntityManager()->persist($entity);
            $flushOnlyEntity
                ? $this->getEntityManager()->flush($entity)
                : $this->getEntityManager()->flush();
        } catch (\Exception $e) {
            throw new \RuntimeException(sprintf('Cannot create entity: %s', $e->getMessage()));
        }

        return $this;
    }

    /**
     * Update an existing entity.
     */
    public function updateOne(EntityInterface $entity, bool $flushOnlyEntity = false): AbstractRepository
    {
        try {
            $flushOnlyEntity
                ? $this->getEntityManager()->flush($entity)
                : $this->getEntityManager()->flush();
        } catch (\Exception $e) {
            throw new \RuntimeException(sprintf('Cannot update entity: %s', $e->getMessage()));
        }

        return $this;
    }

    public function updateAll(): AbstractRepository
    {
        try {
            $this->getEntityManager()->flush();
        } catch (\Exception $e) {
            throw new \RuntimeException(sprintf('Cannot update entity: %s', $e->getMessage()));
        }

        return $this;
    }

    public function deleteOne(EntityInterface $entity): AbstractRepository
    {
        try {
            $this->getEntityManager()->remove($entity);
            $this->getEntityManager()->flush($entity);
        } catch (\Exception $e) {
            throw new \RuntimeException(sprintf('Cannot remove entity: %s', $e->getMessage()));
        }

        return $this;
    }

    /**
     * Save an entity.
     */
    public function save(EntityInterface $entity, bool $flushOnlyEntity = false): AbstractRepository
    {
        if ($entity->hasId()) {
            return $this->updateOne($entity, $flushOnlyEntity);
        }

        return $this->createOne($entity, $flushOnlyEntity);
    }

    /**
     * Create an instance of an entity from its name.
     */
    public function createEntity(): EntityInterface
    {
        $class = $this->getEntityName();

        return new $class();
    }

    public function getUnitOfWork(): UnitOfWork
    {
        return $this->getEntityManager()->getUnitOfWork();
    }
}
