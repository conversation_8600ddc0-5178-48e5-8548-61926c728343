<?php

namespace App\Repository;

use App\Entity\PicbiSociety;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method PicbiSociety|null find($id, $lockMode = null, $lockVersion = null)
 * @method PicbiSociety|null findOneBy(array $criteria, array $orderBy = null)
 * @method PicbiSociety[]    findAll()
 * @method PicbiSociety[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PicbiSocietyRepository extends AbstractRepository
{
    protected ?string $entityClassName = PicbiSociety::class;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PicbiSociety::class);
    }
}
