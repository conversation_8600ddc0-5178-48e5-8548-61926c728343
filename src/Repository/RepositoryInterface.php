<?php
/**
 * This file is subject to the terms and conditions defined in file
 * 'LICENSE.txt', which is part of this source code package.
 *
 * @copyright Prisma Group (C) 2014-2015
 */

namespace App\Repository;

use App\Entity\EntityInterface;

interface RepositoryInterface
{
    public function findMany(array $filter = [], array $sort = [], ?int $limit = null, ?array $offset = null);

    public function getById($id);

    public function createOne(EntityInterface $entity);

    public function updateOne(EntityInterface $entity);

    public function deleteOne(EntityInterface $entity);
}
