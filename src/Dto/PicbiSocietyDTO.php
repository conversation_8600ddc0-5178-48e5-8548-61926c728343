<?php

namespace App\DTO;

use App\Entity\PicbiBrand;
use App\Entity\PicbiContact;
use App\Entity\PicbiSociety;

readonly class PicbiSocietyDTO
{
    public function __construct(
        public string  $name,
        public ?string $type,
        public int     $code,
        public array   $contacts,
        public array   $brands,
        public ?int    $kantarCode,
        public ?string $kantarLabel,
        public ?int    $id,
        public ?string $createDate,
        public ?string $updateDate,
    ) {
    }

    /**
     * Create a DTO from a PicbiSociety entity.
     */
    public static function fromEntity(PicbiSociety $society): self
    {
        return new self(
            name: $society->getName(),
            type: $society->getType(),
            code: $society->getCode(),
            contacts: array_map([self::class, 'transformContact'], $society->getContacts()->toArray()),
            brands: array_map([self::class, 'transformBrand'], $society->getBrands()->toArray()),
            kantarCode: $society->getKantarCode(),
            kantarLabel: $society->getKantarLabel(),
            id: $society->getId(),
            createDate: $society->getCreateDate()?->format('c'),
            updateDate: $society->getUpdateDate()?->format('c'),
        );
    }

    /**
     * Transform a PicbiContact entity to array format.
     */
    private static function transformContact(PicbiContact $contact): array
    {
        $fullName = trim(($contact->getFirstname() ?? '') . ' ' . ($contact->getLastname() ?? ''));
        $fullContact = $fullName . ($contact->getMail() ? ' <' . $contact->getMail() . '>' : '');
        
        return [
            'code' => $contact->getCode(),
            'lastname' => $contact->getLastname(),
            'firstname' => $contact->getFirstname(),
            'mail' => $contact->getMail(),
            'full_name' => $fullName,
            'full_contact' => $fullContact,
        ];
    }

    /**
     * Transform a PicbiBrand entity to array format.
     */
    private static function transformBrand(PicbiBrand $brand): array
    {
        return [
            'code' => $brand->getCode(),
            'name' => $brand->getName(),
            'sub_kantar_code' => $brand->getSubKantarCode(),
            'sub_kantar_label' => $brand->getSubKantarLabel(),
        ];
    }

    /**
     * Convert the DTO to an array for JSON serialization.
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'type' => $this->type,
            'code' => $this->code,
            'contacts' => $this->contacts,
            'brands' => $this->brands,
            'kantar_code' => $this->kantarCode,
            'kantar_label' => $this->kantarLabel,
            'id' => $this->id,
            'create_date' => $this->createDate,
            'update_date' => $this->updateDate,
        ];
    }

    /**
     * Create multiple DTOs from an array of entities.
     *
     * @param PicbiSociety[] $societies
     * @return self[]
     */
    public static function fromEntities(array $societies): array
    {
        return array_map([self::class, 'fromEntity'], $societies);
    }

    /**
     * Convert multiple DTOs to arrays.
     *
     * @param self[] $dtos
     * @return array[]
     */
    public static function toArrays(array $dtos): array
    {
        return array_map(fn(self $dto) => $dto->toArray(), $dtos);
    }
}
