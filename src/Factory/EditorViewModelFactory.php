<?php

namespace App\Factory;

use App\Dto\EditorViewModel;
use App\Entity\Editor;

class EditorViewModelFactory implements ViewModelInterface
{
    use ViewModelTrait;

    public function createFromEntity(Editor $editor): EditorViewModel
    {
        return new EditorViewModel(
            $editor->getId(),
            $editor->getName(),
            $editor->getCreateDate(),
            $editor->getUpdateDate()
        );
    }
}
