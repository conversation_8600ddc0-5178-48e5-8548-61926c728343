<?php

namespace App\Validator;

class AbstractValidator
{
    protected array $messages = [];

    protected array $messageTemplates = [];

    public function getMessages(): array
    {
        return array_unique($this->messages, SORT_REGULAR);
    }

    public function setMessage(string $message, string $key): static
    {
        $this->messages[$key] = $message;

        return $this;
    }

    protected function getErrorMessage(string $key): string
    {
        return $this->messageTemplates[$key];
    }
}
