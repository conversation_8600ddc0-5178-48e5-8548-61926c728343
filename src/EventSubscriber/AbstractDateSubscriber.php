<?php

namespace App\EventSubscriber;

use App\Entity\EntityDateInterface;
use App\Event\AbstractEvent;
use App\Utils\DateUtils;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

abstract class AbstractDateSubscriber implements EventSubscriberInterface
{
    protected static string $eventCreate = 'create';
    protected static string $eventUpdate = 'update';

    public static function initSubscribedEvents(string $eventCreate, string $eventUpdate): array
    {
        return [
            $eventCreate => [
                ['setCreateDate', 0],
            ],
            $eventUpdate => [
                ['setUpdateDate', 0],
            ],
        ];
    }

    public function setCreateDate(AbstractEvent $event): AbstractEvent
    {
        $entity = $event->getEntity();

        if ($entity instanceof EntityDateInterface) {
            $entity->setCreateDate(DateUtils::getNow());
            $entity->setUpdateDate(DateUtils::getNow());
        }

        return $event;
    }

    public function setUpdateDate(AbstractEvent $event): AbstractEvent
    {
        $entity = $event->getEntity();

        if ($entity instanceof EntityDateInterface) {
            $entity->setUpdateDate(DateUtils::getNow());
        }

        return $event;
    }
}
