<?php

namespace App\EventSubscriber\Base;

use App\Entity\Base as BaseEntity;
use App\Event\Base;
use App\EventSubscriber\AbstractSubscriber;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class DeleteSubscriber extends AbstractSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return [
            Base::EVENT_DELETE => [
                ['checkActiveStatus', 10],
            ],
        ];
    }

    public function checkActiveStatus(Base $event): Base
    {
        /** @var BaseEntity $base */
        $base = $event->getEntity();

        // Check if the base is active
        if ($base->isActive()) {
            // Stop event propagation to prevent deletion
            $event->stopPropagation();
            throw new \RuntimeException('Cannot delete an active Base. Please deactivate it first.');
        }

        return $this->result($event, 'Base deletion allowed - not active');
    }
}
