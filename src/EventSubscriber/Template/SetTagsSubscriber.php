<?php

namespace App\EventSubscriber\Template;

use App\Entity\Template as TemplateEntity;
use App\Event\Template;
use App\Filter\FragmentsInHtml;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class SetTagsSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return [
            Template::EVENT_CREATE => [
                ['setTags', 0],
            ],
            Template::EVENT_UPDATE => [
                ['setTags', 0],
            ],
        ];
    }

    public function setTags(Template $event): Template
    {
        /** @var TemplateEntity $template */
        $template = $event->getEntity();
        if (empty($template->getTags()) && $template->hasContent()) {
            /* Extract all tag names used in the content */
            $tagFilter = new FragmentsInHtml();
            $tags = $tagFilter->filter($template->getContent());
            $template->setTags($tags);
        }

        return $event;
    }
}
