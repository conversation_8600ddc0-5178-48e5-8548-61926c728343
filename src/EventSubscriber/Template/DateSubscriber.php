<?php

namespace App\EventSubscriber\Template;

use App\Entity\Template as TemplateEntity;
use App\Event\Template;
use App\EventSubscriber\AbstractDateSubscriber;

/**
 * @method TemplateEntity getEntity
 */
class DateSubscriber extends AbstractDateSubscriber
{
    public static function getSubscribedEvents(): array
    {
        return parent::initSubscribedEvents(Template::EVENT_CREATE, Template::EVENT_UPDATE);
    }
}
