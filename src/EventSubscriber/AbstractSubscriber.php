<?php

namespace App\EventSubscriber;

use App\Event\AbstractEvent;
use App\Response\ListenerExit;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

abstract class AbstractSubscriber implements EventSubscriberInterface
{
    protected array $logContext = [
        'stackdriverOptions' => [
            'labels' => [],
        ],
    ];

    abstract public static function getSubscribedEvents(): array;

    protected function getClass()
    {
        $path = preg_split('/\\\\/', get_class($this));

        return end($path);
    }

    protected function result(AbstractEvent $event, $message): AbstractEvent
    {
        $diff = (microtime(true) - $event->startTime) * 1000;
        $response = new ListenerExit($this->getClass(), $message.sprintf(' (%.2f ms)', $diff));

        $event->addListenerResponse($response);

        return $event;
    }

    protected function getLogContext(): array
    {
        return $this->logContext;
    }

    protected function getNow(): \DateTime
    {
        return new \DateTime('now', new \DateTimeZone('utc'));
    }

    protected function setLabels(array $labels): static
    {
        $this->logContext['stackdriverOptions']['labels'] = $labels;

        return $this;
    }
}
