<?php

namespace App\Controller;

use App\DataTable\Type\BaseDataTableType;
use App\Form\Type\BaseType;
use App\Service\BaseService;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route(path: '/base', name: 'base_')]
class BaseController extends AbstractController
{
    public function __construct(
        protected readonly BaseService $baseService
    ) {
    }

    #[Route(path: '/list', name: 'list')]
    public function list(Request $request, DataTableFactory $datatableFactory): Response
    {
        $datatable = $datatableFactory->createFromType(BaseDataTableType::class)->handleRequest($request);

        if ($datatable->isCallback()) {
            return $datatable->getResponse();
        }

        return $this->render('base/list.html.twig', [
            'datatable' => $datatable,
        ]);
    }

    #[Route(path: '/create', name: 'create')]
    public function create(Request $request): Response
    {
        $base = $this->baseService->create();
        $form = $this->createForm(BaseType::class, $base);

        try {
            $form = $this->handleForm($form, $request);

            if ($form->isSubmitted() && $form->isValid()) {
                $this->addFlash('success', 'Base créée avec succès');

                return $this->redirectToRoute('base_list');
            }
        } catch (\Exception $e) {
            $this->addFlash('danger', 'Impossible de créer la base');
        }

        return $this->render('base/create.html.twig', ['form' => $form]);
    }

    #[Route(path: '/update/{id}', name: 'update')]
    public function update(int $id, Request $request): Response
    {
        $base = $this->baseService->getById($id);
        $form = $this->createForm(BaseType::class, $base);

        try {
            $form = $this->handleForm($form, $request);

            if ($form->isSubmitted() && $form->isValid()) {
                $this->addFlash('success', 'Base mise à jour');

                return $this->redirectToRoute('base_list');
            }
        } catch (\Exception $e) {
            $this->addFlash('danger', 'Impossible de mettre à jour la base');
        }

        return $this->render('base/update.html.twig', [
            'base' => $base,
            'form' => $form,
        ]);
    }

    #[Route(path: '/delete/{id}', name: 'delete', methods: ['delete'])]
    public function delete(int $id, Request $request): JsonResponse
    {
        $base = $this->baseService->getById($id);

        try {
            $this->baseService->delete($base);
        } catch (\RuntimeException $exception) {
            return new JsonResponse([
                'status' => 400, // Bad request for business rule violation
                'title' => 'Error',
                'detail' => $exception->getMessage(),
                'more' => [],
            ], 400);
        } catch (\Exception $exception) {
            return new JsonResponse([
                'status' => 500,
                'title' => 'Error',
                'detail' => 'Cannot delete Base',
                'more' => ['error' => $exception->getMessage()],
            ], 500);
        }

        return new JsonResponse([
            'status' => 200,
            'title' => 'Success',
            'detail' => 'Base has been deleted',
            'more' => [],
        ]);
    }

    private function handleForm(FormInterface $form, Request $request): FormInterface
    {
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $base = $form->getData();

            $this->baseService->save($base);
        }

        return $form;
    }
}
