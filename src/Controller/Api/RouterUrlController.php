<?php

namespace App\Controller\Api;

use App\Twig\RouterUrlExtension;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route(path: '/api', name: 'api_')]
class RouterUrlController extends AbstractController
{
    public function __construct(
        private readonly RouterUrlExtension $routerUrlExtension
    ) {
    }

    #[Route('/router-urls', name: 'router_urls', methods: ['GET'])]
    public function getRouterUrls(Request $request): JsonResponse
    {
        $publicRef = $request->query->get('publicRef');

        if (!$publicRef) {
            return new JsonResponse(['error' => 'Missing publicRef parameter'], 400);
        }

        $routerUrls = $this->routerUrlExtension->getRouterUrls($publicRef);

        return new JsonResponse($routerUrls);
    }
}
