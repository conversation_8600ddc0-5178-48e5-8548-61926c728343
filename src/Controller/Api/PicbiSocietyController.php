<?php

namespace App\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/picbi', name: 'api_picbi_')]
class PicbiSocietyController extends AbstractController
{
    #[Route(path: '/society', name: 'society')]
    public function getList(Request $request): JsonResponse
    {

        return new JsonResponse([]);
    }
}
