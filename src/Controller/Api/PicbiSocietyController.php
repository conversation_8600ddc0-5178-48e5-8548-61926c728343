<?php

namespace App\Controller\Api;

use App\Service\PicbiSocietyService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/picbi', name: 'api_picbi_')]
class PicbiSocietyController extends AbstractController
{
    public function __construct(
        protected readonly PicbiSocietyService $picbiSocietyService,
    ) {
    }

    #[Route(path: '/society', name: 'society', methods: ['POST'])]
    public function getList(Request $request): JsonResponse
    {
        try {
            // Parse JSON payload
            $data = json_decode($request->getContent(), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return new JsonResponse([
                    'status' => 400,
                    'detail' => 'Invalid JSON payload',
                    'more' => []
                ], 400);
            }

            // Extract keyword from payload
            $keyword = $data['keyword'] ?? null;

            if (empty($keyword)) {
                return new JsonResponse([
                    'status' => 400,
                    'detail' => 'Keyword is required',
                    'more' => []
                ], 400);
            }

            // Build filter for the service
            $filter = ['keywords' => $keyword];

            // Call the service method
            $societies = $this->picbiSocietyService->findFromKeywords($filter);

            // Transform entities to response format
            $data = array_map([$this, 'transformSocietyToArray'], $societies);

            return new JsonResponse([
                'status' => 200,
                'detail' => 'Societies found',
                'more' => [
                    'data' => $data
                ]
            ]);

        } catch (\DomainException $e) {
            return new JsonResponse([
                'status' => 400,
                'detail' => $e->getMessage(),
                'more' => []
            ], 400);
        } catch (\Exception $e) {
            return new JsonResponse([
                'status' => 500,
                'detail' => 'Internal server error',
                'more' => ['error' => $e->getMessage()]
            ], 500);
        }
    }
}
