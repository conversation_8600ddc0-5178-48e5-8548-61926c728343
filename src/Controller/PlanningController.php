<?php

namespace App\Controller;

use App\DataTable\Type\PlanningDataTableType;
use App\Entity\Planning;
use App\Form\FormUtils;
use App\Form\Type\PlanningType;
use App\Service\PlanningService;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route(path: '/planning', name: 'planning_')]
class PlanningController extends AbstractController
{
    public function __construct(
        private readonly PlanningService $planningService,
        protected readonly FormUtils $formUtils,
    ) {
    }

    #[Route('', name: 'list')]
    public function list(Request $request, DataTableFactory $dataTableFactory): Response
    {
        $table = $dataTableFactory->createFromType(PlanningDataTableType::class)->handleRequest($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('planning/list.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/create', name: 'create')]
    public function create(Request $request): Response
    {
        $planning = $this->planningService->create();
        $form = $this->getForm($planning, PlanningType::class, $this->getDefaultUrl());

        try {
            $form = $this->handleForm($form, $request);

            if ($form->isSubmitted() && $form->isValid()) {
                $this->addFlash('success', 'Planning créé avec succès');

                return $this->redirectToRoute('planning_list');
            }
        } catch (\Exception $e) {
            $this->addFlash('danger', 'Impossible de créer le planning');
        }

        return $this->render('planning/create.html.twig', ['form' => $form]);
    }

    #[Route('/update/{id}', name: 'update')]
    public function update(Request $request, int $id): Response
    {
        $planning = $this->planningService->getById($id);
        $form = $this->getForm($planning, PlanningType::class, $this->getDefaultUrl());

        try {
            $form = $this->handleForm($form, $request);

            if ($form->isSubmitted() && $form->isValid()) {
                $this->addFlash('success', 'Planning mis à jour avec succès');

                return $this->redirectToRoute('planning_list');
            }
        } catch (\Exception $e) {
            $this->addFlash('danger', 'Impossible de mettre à jour le planning');
        }

        return $this->render('planning/update.html.twig', [
            'planning' => $planning,
            'form' => $form,
        ]);
    }

    #[Route('/delete/{id}', name: 'delete', methods: ['delete'])]
    public function delete(int $id): JsonResponse
    {
        $planning = $this->planningService->getById($id);

        try {
            $this->planningService->delete($planning);
        } catch (\Exception $exception) {
            return new JsonResponse([
                'status' => 500,
                'title' => 'Error',
                'detail' => 'Cannot delete Planning',
                'more' => ['error' => $exception->getMessage()],
            ]);
        }

        return new JsonResponse([
            'status' => 200,
            'title' => 'Success',
            'detail' => 'Planning deleted successfully',
            'more' => [],
        ]);
    }

    protected function getForm(Planning $planning, string $formClass, string $defaultUrl): FormInterface
    {
        $form = $this->createForm($formClass, $planning);
        $form = $this->formUtils->addCancelButton($form, $defaultUrl);

        return $this->formUtils->addSaveButton($form);
    }

    private function handleForm(FormInterface $form, Request $request): FormInterface
    {
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $planning = $form->getData();

            $this->planningService->save($planning);
        }

        return $form;
    }

    private function getDefaultUrl(): string
    {
        return $this->generateUrl('planning_list');
    }
}
