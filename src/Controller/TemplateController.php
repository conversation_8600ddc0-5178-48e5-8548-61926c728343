<?php

namespace App\Controller;

use App\DataTable\Type\TemplateDataTableType;
use App\Entity\Template;
use App\Form\Type\TemplateContentType;
use App\Form\Type\TemplateType;
use App\Service\TemplateService;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route(path: '/template', name: 'template_')]
class TemplateController extends AbstractController
{
    public function __construct(
        protected readonly TemplateService $templateService
    ) {
    }

    #[Route(path: '/list', name: 'list')]
    public function list(Request $request, DataTableFactory $dataTableFactory): Response
    {
        $datatable = $dataTableFactory->createFromType(TemplateDataTableType::class)->handleRequest($request);

        if ($datatable->isCallback()) {
            return $datatable->getResponse();
        }

        return $this->render('template/list.html.twig', [
            'datatable' => $datatable,
        ]);
    }

    #[Route(path: '/create', name: 'create')]
    public function create(Request $request): Response
    {
        /** @var Template $template */
        $template = $this->templateService->create();
        $form = $this->createForm(TemplateType::class, $template);

        try {
            $form = $this->handleForm($form, $request);

            if ($form->isSubmitted() && $form->isValid()) {
                $this->addFlash('success', 'Éditeur créé avec succès');

                return $this->redirectToRoute('template_update_content', [
                    'id' => $template->getId(),
                ]);
            }
        } catch (\Exception $e) {
            $this->addFlash('danger', 'Impossible de créer l\'éditeur');
        }

        return $this->render('template/create.html.twig', ['form' => $form]);
    }

    #[Route(path: '/update/content/{id}', name: 'update_content')]
    public function updateContent(int $id, Request $request): Response
    {
        /** @var Template $template */
        $template = $this->templateService->getById($id);
        $form = $this->createForm(TemplateContentType::class, $template);

        return $this->render('template/editor.html.twig', [
            'template' => $template,
            'template_urls' => $this->templateService->getNLFactoryTemplateUrls(),
            'form' => $form,
        ]);
    }

    #[Route(path: '/update/{id}', name: 'update')]
    public function update(int $id, Request $request): Response
    {
        /** @var Template $template */
        $template = $this->templateService->getById($id);
        $form = $this->createForm(TemplateType::class, $template);

        try {
            $form = $this->handleForm($form, $request);
            if ($form->isSubmitted() && $form->isValid()) {
                $this->addFlash('success', 'le template est mis à jour');

                return $this->redirectToRoute('template_list');
            }
        } catch (\Exception $e) {
            $this->addFlash('danger', 'Impossible de mettre à jour le template');
        }

        return $this->render('template/update.html.twig', [
            'template' => $template,
            'form' => $form,
        ]);
    }

    #[Route(path: '/delete/{id}', name: 'delete', methods: ['delete'])]
    public function delete(int $id, Request $request): JsonResponse
    {
        /** @var Template $template */
        $template = $this->templateService->getById($id);

        try {
            $this->templateService->delete($template);
        } catch (\Exception $exception) {
            return new JsonResponse([
                'status' => 500,
                'title' => 'Error',
                'detail' => 'Cannot delete Template',
                'more' => ['error' => $exception->getMessage()],
            ], 500);
        }

        return new JsonResponse([
            'status' => 200,
            'title' => 'Success',
            'detail' => 'Template has been deleted',
            'more' => [],
        ]);
    }

    private function handleForm(FormInterface $form, Request $request): FormInterface
    {
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $template = $form->getData();

            $this->templateService->save($template);
        }

        return $form;
    }
}
