<?php

namespace App\Controller;

use App\DataTable\Type\EditorDataTableType;
use App\Entity\Editor;
use App\Form\Type\EditorType;
use App\Service\EditorService;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route(path: '/editor', name: 'editor_')]
class EditorController extends AbstractController
{
    public function __construct(
        protected readonly EditorService $editorService
    ) {
    }

    #[Route(path: '/list', name: 'list')]
    public function list(Request $request, DataTableFactory $dataTableFactory): Response
    {
        $datatable = $dataTableFactory->createFromType(EditorDataTableType::class)->handleRequest($request);

        if ($datatable->isCallback()) {
            return $datatable->getResponse();
        }

        return $this->render('editor/list.html.twig', [
            'datatable' => $datatable,
        ]);
    }

    #[Route(path: '/create', name: 'create')]
    public function create(Request $request): Response
    {
        /** @var Editor $editor */
        $editor = $this->editorService->create();
        $form = $this->createForm(EditorType::class, $editor);

        try {
            $form = $this->handleForm($form, $request);

            if ($form->isSubmitted() && $form->isValid()) {
                $this->addFlash('success', 'Éditeur créé avec succès');

                return $this->redirectToRoute('editor_list');
            }
        } catch (\Exception $e) {
            $this->addFlash('danger', 'Impossible de créer l\'éditeur');
        }

        return $this->render('editor/create.html.twig', ['form' => $form]);
    }

    #[Route(path: '/update/{id}', name: 'update')]
    public function update(int $id, Request $request): Response
    {
        /** @var Editor $editor */
        $editor = $this->editorService->getById($id);
        $form = $this->createForm(EditorType::class, $editor);

        try {
            $form = $this->handleForm($form, $request);

            if ($form->isSubmitted() && $form->isValid()) {
                $this->addFlash('success', 'Éditeur mis à jour');

                return $this->redirectToRoute('editor_list');
            }
        } catch (\Exception $e) {
            $this->addFlash('danger', 'Impossible de mettre à jour l\'éditeur');
        }

        return $this->render('editor/update.html.twig', [
            'editor' => $editor,
            'form' => $form,
        ]);
    }

    #[Route(path: '/delete/{id}', name: 'delete')]
    public function delete(int $id, Request $request): JsonResponse
    {
        /** @var Editor $editor */
        $editor = $this->editorService->getById($id);

        try {
            $this->editorService->remove($editor);
        } catch (\Exception $exception) {
            return new JsonResponse([
                'status' => 500,
                'title' => 'Error',
                'detail' => 'Cannot delete Editor',
                'more' => ['error' => $exception->getMessage()],
            ]);
        }

        return new JsonResponse([
            'status' => 200,
            'title' => 'Success',
            'detail' => 'Editor has been deleted',
            'more' => [],
        ]);
    }

    private function handleForm(FormInterface $form, Request $request): FormInterface
    {
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $editor = $form->getData();

            $this->editorService->save($editor);
        }

        return $form;
    }
}
