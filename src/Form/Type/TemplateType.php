<?php

namespace App\Form\Type;

use App\Entity\Template;
use Symfony\Component\Form\Extension\Core\Type;
use Symfony\Component\Form\FormBuilderInterface;

class TemplateType extends AbstractFormType
{
    protected ?string $entityClass = Template::class;

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', Type\TextType::class, [
                'label' => 'Nom',
            ])
            ->add('cancel', Type\ButtonType::class, [
                'label' => 'Annuler',
                'attr' => [
                    'class' => 'btn btn-outline-secondary',
                    'onClick' => 'window.history.back();',
                ],
            ])
            ->add('save', Type\SubmitType::class, [
                'label' => 'Enregistrer',
                'attr' => [
                    'class' => 'btn btn-primary',
                ],
            ])
        ;
    }
}
