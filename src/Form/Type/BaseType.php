<?php

namespace App\Form\Type;

use App\Entity\Base;
use App\Entity\Editor;
use App\Entity\Template;
use App\Service\EmailConsentService;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class BaseType extends AbstractType
{
    public function __construct(
        protected EmailConsentService $emailConsentService,
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('label', Type\TextType::class, [
                'label' => 'Label',
                'required' => true,
            ])
            ->add('defaultTemplate', EntityType::class, [
                'class' => Template::class,
                'choice_label' => 'name',
                'label' => 'Template',
                'placeholder' => '',
                'required' => false,
            ])
            ->add('routerUrl', Type\ChoiceType::class, [
                'label' => 'URL routeur',
                'choices' => $this->getRouterUrlChoices($this->emailConsentService->getUnusedConsents()),
                'placeholder' => '',
                'required' => false,
            ])
            ->add('active', Type\ChoiceType::class, [
                'label' => 'Active',
                'choices' => [
                    'Oui' => true,
                    'Non' => false,
                ],
                'expanded' => true,
            ])
            ->add('shootHour', Type\TimeType::class, [
                'label' => 'Shoot hour',
                'input' => 'string',
                'input_format' => 'H:i',
                'required' => false,
            ])
            ->add('category', Type\ChoiceType::class, [
                'label' => 'Catégorie de la base',
                'choices' => [
                    'NEWSLETTER' => 'NEWSLETTER',
                ],
                'required' => true,
            ])
            ->add('editor', EntityType::class, [
                'class' => Editor::class,
                'choice_label' => 'name',
                'label' => 'Éditeur',
                'placeholder' => '',
                'required' => false,
            ])
            ->add('router', Type\ChoiceType::class, [
                'label' => 'Routeur',
                'choices' => [
                    '---' => '',
                    'Splio' => 'Splio',
                    'Webrivage' => 'Webrivage',
                ],
                'required' => true,
            ])
            ->add('consentRef', Type\TextType::class, [
                'label' => 'Consent ID <br/><small>(karinto publicId)</small>',
                'label_html' => true,
                'disabled' => true,
            ])
            ->add('mainDomain', Type\TextType::class, [
                'label' => 'Domaine',
                'required' => true,
            ])
            ->add('sendingDomain', Type\TextType::class, [
                'label' => 'Domaine d\'envoi',
                'required' => true,
            ])
            ->add('senderEmail', Type\TextType::class, [
                'label' => 'Adresse d\'envoi',
                'required' => true,
            ])
            ->add('imageDomain', Type\TextType::class, [
                'label' => 'Domaine image',
                'required' => true,
            ])
            ->add('replyTo', Type\TextType::class, [
                'label' => 'Adresse de réponse',
                'required' => true,
            ])
            ->add('frequency', Type\ChoiceType::class, [
                'label' => 'Fréquence',
                'choices' => [
                    'Quotidien' => 'DAILY',
                    'Quotidien ouvré' => 'BUSINESS_DAILY',
                    'Bi-hebdomadaire' => 'BI_WEEKLY',
                    'Hebdomadaire' => 'WEEKLY',
                    'Bi-mensuel' => 'BI_MONTHLY',
                    'Mensuel' => 'MONTHLY',
                    'Événementiel' => 'EVENT',
                ],
                'choice_attr' => [
                    'Quotidien' => ['data-week-days' => '1,2,3,4,5,6,7'],
                    'Quotidien ouvré' => ['data-week-days' => '1,2,3,4,5'],
                    'Bi-hebdomadaire' => ['data-week-days' => '1,3'],
                    'Hebdomadaire' => ['data-week-days' => '1'],
                    'Bi-mensuel' => ['data-week-days' => ''],
                    'Mensuel' => ['data-week-days' => ''],
                    'Événementiel' => ['data-week-days' => ''],
                ],
                'expanded' => true,
                'required' => true,
            ])
            ->add('restrictedDays', Type\ChoiceType::class, [
                'label' => 'Jours de publication',
                'choices' => $this->getChoiceDays(),
                'multiple' => true,
                'expanded' => true,
                'required' => false,
            ])
            ->add('autoCreationDays', Type\ChoiceType::class, [
                'label' => 'Jours de création',
                'choices' => $this->getChoiceDays(),
                'multiple' => true,
                'expanded' => true,
                'required' => false,
            ])
            ->add('autoValidationDays', Type\ChoiceType::class, [
                'label' => 'Jours de validation',
                'choices' => $this->getChoiceDays(),
                'multiple' => true,
                'expanded' => true,
                'required' => false,
            ])
            ->add('maxEmailsPerDay', Type\IntegerType::class, [
                'label' => 'Envois Max par jour',
                'attr' => [
                    'min' => 1,
                    'max' => 10,
                    'maxlength' => '2',
                    'style' => 'max-width: 80px;',
                ],
            ])
            ->add('cancel', Type\ButtonType::class, [
                'label' => 'Annuler',
                'attr' => [
                    'class' => 'btn btn-outline-secondary',
                    'onClick' => 'window.history.back();',
                ],
            ])
            ->add('save', Type\SubmitType::class, [
                'label' => 'Enregistrer',
                'attr' => [
                    'class' => 'btn btn-primary',
                ],
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Base::class,
        ]);
    }

    private function getChoiceDays(): array
    {
        return [
            'Lundi' => 1,
            'Mardi' => 2,
            'Mercredi' => 3,
            'Jeudi' => 4,
            'Vendredi' => 5,
            'Samedi' => 6,
            'Dimanche' => 7,
        ];
    }

    private function getRouterUrlChoices(array $unusedUrls): array
    {
        $choices = [];

        foreach ($unusedUrls as $publicRef) {
            $choices[$publicRef] = $publicRef;
        }

        return $choices;
    }
}
