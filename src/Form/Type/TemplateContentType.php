<?php

namespace App\Form\Type;

use App\Entity\Template;
use Symfony\Component\Form\Extension\Core\Type;
use Symfony\Component\Form\FormBuilderInterface;

class TemplateContentType extends AbstractFormType
{
    protected ?string $entityClass = Template::class;
    private array $contentTags;

    public function __construct(array $contentTags)
    {
        $this->contentTags = $contentTags['content'];
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('tags', Type\HiddenType::class, [
                'attr' => [
                    'value' => json_encode($this->contentTags),
                ],
            ])
        ;
        //        $builder->add('content', Type\TextareaType::class);
    }
}
