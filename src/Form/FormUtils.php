<?php

namespace App\Form;

use Symfony\Component\Form\Extension\Core\Type\ButtonType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormInterface;

class FormUtils
{
    public function addSaveButton(FormInterface $form): FormInterface
    {
        return $form->add('save', SubmitType::class, [
            'label' => 'Enregistrer',
            'attr' => [
                'style' => 'float: right',
                'class' => 'btn btn-primary',
            ],
        ]);
    }

    public function addCancelButton(FormInterface $form, string $path): FormInterface
    {
        return $form->add('cancel', ButtonType::class, [
            'label' => 'Annuler',
            'attr' => [
                'onclick' => 'location.href = "'.$path.'"',
                'style' => 'float: left',
                'class' => 'btn btn-outline-secondary',
            ],
        ]);
    }
}
