<?php

namespace App\DataTable\Type;

use App\Entity\Base;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableTypeInterface;

class BaseDataTableType implements DataTableTypeInterface
{
    public function configure(DataTable $dataTable, array $options): void
    {
        $dataTable
            ->setName('base-list')
            ->add('id', Column\NumberColumn::class, [
                'label' => 'id',
                'visible' => false,
                'globalSearchable' => false,
                'searchable' => false,
            ])
            ->add('label', Column\TextColumn::class, ['label' => 'Label'])
            ->add('defaultTemplateName', Column\TextColumn::class, ['label' => 'Template par défaut'])
            ->add('editorName', Column\TextColumn::class, ['label' => 'Éditeur'])
            ->add('active', Column\BoolColumn::class, [
                'label' => 'Active',
                'searchable' => false,
                'orderable' => true,
                'globalSearchable' => true,
                'trueValue' => '1',
                'falseValue' => '-1',
                'render' => function ($value) {
                    if ('1' === $value) {
                        return '<span class="badge bg-primary">Oui</span>';
                    }

                    return '<span class="badge bg-secondary">Non</span>';
                },
            ])
            ->add('routerUrl', Column\TextColumn::class, ['label' => 'URL routeur'])
            ->add('createDate', Column\DateTimeColumn::class, [
                'label' => 'Date de création',
                'format' => 'd/m/Y H:i',
                'orderable' => true,
                'searchable' => false,
                'globalSearchable' => false,
                'render' => function ($value, $context) {
                    if ($context->getCreateDate()) {
                        $date = clone $context->getCreateDate();

                        return $date->setTimezone(new \DateTimeZone('Europe/Paris'))->format('d/m/Y H:i');
                    }

                    return '';
                },
            ])
            ->add('updateDate', Column\DateTimeColumn::class, [
                'label' => 'Date de Modification',
                'format' => 'd/m/Y H:i',
                'orderable' => true,
                'searchable' => false,
                'globalSearchable' => false,
                'render' => function ($value, $context) {
                    if ($context->getUpdateDate()) {
                        $date = clone $context->getUpdateDate();

                        return $date->setTimezone(new \DateTimeZone('Europe/Paris'))->format('d/m/Y H:i');
                    }

                    return '';
                },
            ])
            ->add('actions', Column\TwigColumn::class, [
                'label' => 'Actions',
                'template' => 'base/list_actions.html.twig',
                'orderable' => false,
                'searchable' => false,
            ])
            ->createAdapter(ORMAdapter::class, [
                'entity' => Base::class,
            ]);
    }
}
