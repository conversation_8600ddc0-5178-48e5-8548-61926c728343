<?php

namespace App\DataTable\Type;

use App\Entity\Editor;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableTypeInterface;

class EditorDataTableType implements DataTableTypeInterface
{
    public function configure(DataTable $dataTable, array $options): void
    {
        $dataTable
            ->setName('editor-list')
            ->add('id', Column\NumberColumn::class, [
                'label' => 'id',
                'visible' => false,
                'globalSearchable' => false,
                'searchable' => false,
            ])
            ->add('name', Column\TextColumn::class, ['label' => 'Nom'])
            ->add('createDate', Column\DateTimeColumn::class, [
                'label' => 'Date de création',
                'format' => 'd/m/Y H:i',
                'orderable' => true,
                'searchable' => true,
                'globalSearchable' => false,
                'render' => function ($value, $context) {
                    if ($context->getCreateDate()) {
                        $date = clone $context->getCreateDate();

                        return $date->setTimezone(new \DateTimeZone('Europe/Paris'))->format('d/m/Y H:i');
                    }

                    return '';
                },
            ])
            ->add('updateDate', Column\DateTimeColumn::class, [
                'label' => 'Date de Modification',
                'format' => 'd/m/Y H:i',
                'orderable' => true,
                'searchable' => true,
                'globalSearchable' => false,
                'render' => function ($value, $context) {
                    if ($context->getUpdateDate()) {
                        $date = clone $context->getUpdateDate();

                        return $date->setTimezone(new \DateTimeZone('Europe/Paris'))->format('d/m/Y H:i');
                    }

                    return '';
                },
            ])
            ->add('actions', Column\TwigColumn::class, [
                'label' => 'Actions',
                'template' => 'partials/components/_list_actions.html.twig',
                'orderable' => false,
                'searchable' => false,
            ])
            ->createAdapter(ORMAdapter::class, [
                'entity' => Editor::class,
            ]);
    }
}
