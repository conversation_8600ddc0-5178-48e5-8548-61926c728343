<?php

namespace App\DataTable\Type;

use App\Entity\Planning;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\Column\TwigColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableTypeInterface;

class PlanningDataTableType implements DataTableTypeInterface
{
    public function configure(DataTable $dataTable, array $options): void
    {
        $dataTable
            ->add('name', TextColumn::class, [
                'label' => 'Nom',
                'searchable' => true,
            ])
            ->add('bases', TextColumn::class, [
                'label' => 'Nombre de bases',
                'render' => function ($value, $context) {
                    return $context->getBases()->count();
                },
                'searchable' => false,
            ])
            ->add('updateDate', DateTimeColumn::class, [
                'label' => 'Dernière modification',
                'format' => 'd/m/Y H:i',
                'searchable' => false,
            ])
            ->add('actions', TwigColumn::class, [
                'label' => 'Actions',
                'template' => 'planning/list_actions.html.twig',
                'className' => 'text-right',
            ])
            ->createAdapter(ORMAdapter::class, [
                'entity' => Planning::class,
            ]);
    }
}
