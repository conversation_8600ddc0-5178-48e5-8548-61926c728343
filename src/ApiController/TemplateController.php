<?php

namespace App\ApiController;

use App\Entity\Template;
use App\Service\TemplateService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/template', name: 'api_template_')]
class TemplateController extends AbstractController
{
    public function __construct(
        protected readonly TemplateService $templateService
    ) {
    }

    #[Route(path: '/update/{id}/content', name: 'update_content', methods: ['put'])]
    public function updateContent(int $id, Request $request): Response
    {
        /** @var Template $template */
        $template = $this->templateService->getById($id);
        $content = $request->request->get('content');

        if (null === $content) {
            return new JsonResponse([
                'status' => 400,
                'title' => 'Bad Request',
                'detail' => 'Le contenu est manquant.',
            ], 400);
        }

        try {
            $this->templateService->saveContent($template, $content);
        } catch (\Exception $exception) {
            return new JsonResponse([
                'status' => 500,
                'title' => 'Error',
                'detail' => 'Cannot save Template',
                'more' => ['error' => $exception->getMessage()],
            ], 500);
        }

        return new JsonResponse([
            'status' => 200,
            'title' => 'Success',
            'detail' => 'Le contenu du template est mis à jour.',
            'more' => [],
        ]);
    }
}
