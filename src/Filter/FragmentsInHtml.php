<?php

namespace App\Filter;

use App\Entity\ContentInterface;

class FragmentsInHtml implements FilterInterface
{
    /**
     * Catch <!-- START OF $#$FRAGMENT_NAME$#$ -->.
     */
    public const REGEX_START_OF = '/<!-- START OF ([#][^ ]{1,70}|[$][#][$][^$ ]{1,70}[$][#][$]) -->/';

    public function filter(mixed $content): array
    {
        if ($content instanceof ContentInterface) {
            $content = $content->getContent();
        }
        if (!is_string($content)) {
            throw new \InvalidArgumentException(sprintf('FragmentsInHtml expects $content to be a string ; %s given', is_object($content) ? get_class($content) : gettype($content)));
        }

        $vars = [];
        if (false !== preg_match_all(static::REGEX_START_OF, $content, $matches)) {
            $vars = array_unique($matches[1]);
        }

        return $vars;
    }
}
