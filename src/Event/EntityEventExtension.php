<?php

namespace App\Event;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;

readonly class EntityEventExtension
{
    public function __construct(private EventDispatcherInterface $eventDispatcher)
    {
    }

    final public function preCreate(object $object, array $options = []): void
    {
        $this->eventDispatcher->dispatch(
            new PersistenceEvent($object, $options, PersistenceEvent::TYPE_PRE_CREATE),
            'app.event.persistence.pre_create'
        );
        $this->eventDispatcher->dispatch(
            new PersistenceEvent($object, $options, PersistenceEvent::TYPE_PRE_PERSIST),
            'app.event.persistence.pre_persist'
        );
    }

    final public function postCreate(object $object, array $options = []): void
    {
        $this->eventDispatcher->dispatch(
            new PersistenceEvent($object, $options, PersistenceEvent::TYPE_POST_CREATE),
            'app.event.persistence.post_create'
        );
        $this->eventDispatcher->dispatch(
            new PersistenceEvent($object, $options, PersistenceEvent::TYPE_POST_PERSIST),
            'app.event.persistence.post_persist'
        );
    }

    final public function preUpdate(object $object, array $options = []): void
    {
        $this->eventDispatcher->dispatch(
            new PersistenceEvent($object, $options, PersistenceEvent::TYPE_PRE_UPDATE),
            'app.event.persistence.pre_update'
        );
        $this->eventDispatcher->dispatch(
            new PersistenceEvent($object, $options, PersistenceEvent::TYPE_PRE_PERSIST),
            'app.event.persistence.pre_persist'
        );
    }

    final public function postUpdate(object $object, array $options = []): void
    {
        $this->eventDispatcher->dispatch(
            new PersistenceEvent($object, $options, PersistenceEvent::TYPE_POST_UPDATE),
            'app.event.persistence.post_update'
        );
        $this->eventDispatcher->dispatch(
            new PersistenceEvent($object, $options, PersistenceEvent::TYPE_POST_PERSIST),
            'app.event.persistence.post_persist'
        );
    }

    final public function preRemove(object $object, array $options = []): void
    {
        $this->eventDispatcher->dispatch(
            new PersistenceEvent($object, $options, PersistenceEvent::TYPE_PRE_REMOVE),
            'app.event.persistence.pre_remove'
        );
    }

    final public function postRemove(object $object, array $options = []): void
    {
        $this->eventDispatcher->dispatch(
            new PersistenceEvent($object, $options, PersistenceEvent::TYPE_POST_REMOVE),
            'app.event.persistence.post_remove'
        );
    }
}
