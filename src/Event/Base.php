<?php

namespace App\Event;

class Base extends AbstractEvent
{
    public const string EVENT_CREATE = 'base.create';
    public const string EVENT_CREATE_POST = 'base.create.post';
    public const string EVENT_GET_POST = 'base.get.post';
    public const string EVENT_UPDATE = 'base.update';
    public const string EVENT_UPDATE_POST = 'base.update.post';
    public const string EVENT_DELETE = 'base.delete';
    public const string EVENT_DELETE_POST = 'base.delete.post';

    protected array $options = [];

    public function getOptions(): array
    {
        return $this->options ?? [];
    }

    public function setOptions(array $options): self
    {
        $this->options = $options;

        return $this;
    }
}
