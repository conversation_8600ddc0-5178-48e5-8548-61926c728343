<?php

namespace App\Event;

class Template extends AbstractEvent
{
    public const EVENT_CREATE = 'template.create';
    public const EVENT_CREATE_POST = 'template.create.post';
    public const EVENT_GET_POST = 'template.get.post';
    public const EVENT_UPDATE = 'template.update';
    public const EVENT_UPDATE_POST = 'template.update.post';
    public const EVENT_DELETE = 'template.delete';
    public const EVENT_DELETE_POST = 'template.delete.post';

    protected array $options = [];

    public function getOptions(): array
    {
        return $this->options ?? [];
    }

    public function setOptions(array $options): self
    {
        $this->options = $options;

        return $this;
    }
}
