<?php

namespace App\Event;

use Symfony\Contracts\EventDispatcher\Event;

class PersistenceEvent extends Event
{
    public const string TYPE_PRE_CREATE = 'PRE_CREATE';
    public const string TYPE_POST_CREATE = 'POST_CREATE';
    public const string TYPE_PRE_PERSIST = 'PRE_PERSIST';
    public const string TYPE_POST_PERSIST = 'POST_PERSIST';
    public const string TYPE_PRE_UPDATE = 'PRE_UPDATE';
    public const string TYPE_POST_UPDATE = 'POST_UPDATE';
    public const string TYPE_PRE_REMOVE = 'PRE_REMOVE';
    public const string TYPE_POST_REMOVE = 'POST_REMOVE';

    public function __construct(
        private readonly object $object,
        private readonly array $options,
        private readonly string $type,
    ) {
    }

    public function getObject(): object
    {
        return $this->object;
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function getType(): string
    {
        return $this->type;
    }
}
