<?php

namespace App\Event;

use App\Entity\EntityInterface;
use Symfony\Contracts\EventDispatcher\Event;

class AbstractEvent extends Event implements EventInterface
{
    public ?float $startTime = null;
    protected ?EntityInterface $entity = null;
    protected ?string $error = null;

    public function startTimer(): void
    {
        $this->startTime = microtime(true);
    }

    public function getEntity(): ?EntityInterface
    {
        return $this->entity;
    }

    public function setEntity(?EntityInterface $entity): EventInterface
    {
        $this->entity = $entity;

        return $this;
    }

    public function getError(): ?string
    {
        return $this->error;
    }

    public function setError(string $error): EventInterface
    {
        $this->error = $error;

        return $this;
    }
}
