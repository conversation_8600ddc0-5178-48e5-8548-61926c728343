<?php

namespace App\Entity;

class AbstractContent extends AbstractEntity implements ContentInterface
{
    protected ?array $tags = [];

    protected ?string $content;

    public function hasContent(): bool
    {
        return isset($this->content);
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function getTags(): ?array
    {
        return $this->tags;
    }

    public function setTags(?array $tags): self
    {
        $this->tags = $tags;

        return $this;
    }
}
