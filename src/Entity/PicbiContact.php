<?php

namespace App\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'picbi_contact', schema: 'rogue_one__picbi')]
class PicbiContact extends AbstractEntity
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER)]
    private int $code;

    #[ORM\Column(type: Types::STRING, length: 100, nullable: true)]
    private ?string $lastname = null;

    #[ORM\Column(type: Types::STRING, length: 100, nullable: true)]
    private ?string $firstname = null;

    #[ORM\Column(type: Types::STRING, length: 250, nullable: true)]
    private ?string $mail = null;

    #[ORM\ManyToOne(inversedBy: 'contacts')]
    #[ORM\JoinColumn(name: 'picbi_society_id', referencedColumnName: 'id')]
    private ?PicbiSociety $picbiSociety = null;

    public function getCode(): int
    {
        return $this->code;
    }

    public function setCode(int $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getLastname(): ?string
    {
        return $this->lastname;
    }

    public function setLastname(?string $lastname): self
    {
        $this->lastname = $lastname;

        return $this;
    }

    public function getFirstname(): ?string
    {
        return $this->firstname;
    }

    public function setFirstname(?string $firstname): self
    {
        $this->firstname = $firstname;

        return $this;
    }

    public function getMail(): ?string
    {
        return $this->mail;
    }

    public function setMail(?string $mail): self
    {
        $this->mail = $mail;

        return $this;
    }

    public function getPicbiSociety(): ?PicbiSociety
    {
        return $this->picbiSociety;
    }

    public function setPicbiSociety(?PicbiSociety $picbiSociety): self
    {
        $this->picbiSociety = $picbiSociety;

        return $this;
    }
}
