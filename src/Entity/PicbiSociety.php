<?php

namespace App\Entity;

use App\Repository\PicbiSocietyRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PicbiSocietyRepository::class)]
#[ORM\Table(name: 'picbi_society', schema: 'rogue_one__picbi')]
class PicbiSociety extends AbstractEntity
{
    use TimestampableEntityTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\Column(type: Types::INTEGER)]
    #[ORM\SequenceGenerator(sequenceName: 'picbi_society_id_seq', allocationSize: 100, initialValue: 1)]
    protected ?int $id = null;

    #[ORM\Column(type: Types::STRING, length: 100, unique: true)]
    protected string $name;

    #[ORM\Column(type: Types::STRING, length: 10, nullable: true)]
    protected ?string $type = null;

    #[ORM\Column(type: Types::INTEGER)]
    protected int $code;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $kantarCode = null;

    #[ORM\Column(type: Types::STRING, length: 100, nullable: true)]
    protected ?string $kantarLabel = null;

    #[ORM\OneToMany(targetEntity: PicbiContact::class, mappedBy: 'picbiSociety')]
    private Collection $contacts;

    #[ORM\OneToMany(targetEntity: PicbiBrand::class, mappedBy: 'picbiSociety')]
    private Collection $brands;

    public function __construct()
    {
        $this->contacts = new ArrayCollection();
        $this->brands = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function setCode(int $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getKantarCode(): ?int
    {
        return $this->kantarCode;
    }

    public function setKantarCode(?int $kantarCode): self
    {
        $this->kantarCode = $kantarCode;

        return $this;
    }

    public function getKantarLabel(): ?string
    {
        return $this->kantarLabel;
    }

    public function setKantarLabel(?string $kantarLabel): self
    {
        $this->kantarLabel = $kantarLabel;

        return $this;
    }

    /**
     * @return Collection<int, PicbiContact>
     */
    public function getContacts(): Collection
    {
        return $this->contacts;
    }

    public function addContact(PicbiContact $contact): self
    {
        if (!$this->contacts->contains($contact)) {
            $this->contacts->add($contact);
            $contact->setPicbiSociety($this);
        }

        return $this;
    }

    public function removeContact(PicbiContact $contact): self
    {
        if ($this->contacts->removeElement($contact)) {
            // set the owning side to null (unless already changed)
            if ($contact->getPicbiSociety() === $this) {
                $contact->setPicbiSociety(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, PicbiBrand>
     */
    public function getBrands(): Collection
    {
        return $this->brands;
    }

    public function addBrand(PicbiBrand $brand): self
    {
        if (!$this->brands->contains($brand)) {
            $this->brands->add($brand);
            $brand->setPicbiSociety($this);
        }

        return $this;
    }

    public function removeBrand(PicbiBrand $brand): self
    {
        if ($this->brands->removeElement($brand)) {
            // set the owning side to null (unless already changed)
            if ($brand->getPicbiSociety() === $this) {
                $brand->setPicbiSociety(null);
            }
        }

        return $this;
    }
}
