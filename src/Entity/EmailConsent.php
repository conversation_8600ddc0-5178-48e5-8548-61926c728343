<?php

namespace App\Entity;

use App\Repository\EmailConsentRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EmailConsentRepository::class)]
#[ORM\Table(name: 'email_consent', schema: 'karinto')]
class EmailConsent extends AbstractEntity
{
    #[ORM\Id]
    #[ORM\Column(type: 'integer', nullable: false)]
    protected ?int $id;

    #[ORM\Column(name: 'public_ref', type: 'string', nullable: false)]
    protected string $publicRef;

    #[ORM\Column(type: 'string', nullable: false)]
    protected string $type;

    #[ORM\Column(type: 'boolean', nullable: false)]
    protected bool $active;

    #[ORM\Column(type: 'string')]
    protected ?string $label;

    #[ORM\Column(name: 'unsubscribe_domain', type: 'string')]
    protected ?string $unsubDomain;

    #[ORM\Column(name: 'public_id', type: 'integer')]
    protected ?int $publicId;

    public function getPublicRef(): string
    {
        return $this->publicRef;
    }

    public function setPublicRef(string $publicRef): static
    {
        $this->publicRef = $publicRef;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): static
    {
        $this->active = $active;

        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(?string $label): static
    {
        $this->label = $label;

        return $this;
    }

    public function getUnsubDomain(): ?string
    {
        return $this->unsubDomain;
    }

    public function setUnsubDomain(?string $unsubDomain): static
    {
        $this->unsubDomain = $unsubDomain;

        return $this;
    }

    public function getPublicId(): ?int
    {
        return $this->publicId;
    }

    public function setPublicId(?int $publicId): static
    {
        $this->publicId = $publicId;

        return $this;
    }
}
