<?php

namespace App\Entity;

use App\Repository\EditorRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EditorRepository::class)]
#[ORM\Table(name: 'editor', schema: 'rogue_one')]
#[ORM\HasLifecycleCallbacks]
class Editor extends AbstractEntity implements EntityDateInterface
{
    use TimestampableEntityTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\SequenceGenerator(sequenceName: 'rogue_one.editor_id_seq', allocationSize: 100, initialValue: 1)]
    #[ORM\Column(type: 'integer', nullable: false)]
    protected ?int $id;

    #[ORM\Column(type: 'string', length: 255, nullable: false)]
    protected string $name;

    public function __toString(): string
    {
        return $this->name;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }
}
