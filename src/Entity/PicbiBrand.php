<?php

namespace App\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'picbi_brand', schema: 'rogue_one__picbi')]
class <PERSON><PERSON><PERSON><PERSON>rand extends AbstractEntity
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER)]
    protected int $code;

    #[ORM\Column(type: Types::STRING, length: 100, nullable: true)]
    protected ?string $name = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    protected ?int $subKantarCode = null;

    #[ORM\Column(type: Types::STRING, length: 100, nullable: true)]
    protected ?string $subKantarLabel = null;

    #[ORM\ManyToOne(inversedBy: 'brands')]
    #[ORM\JoinColumn(name: 'picbi_society_id', referencedColumnName: 'id')]
    protected ?PicbiSociety $picbiSociety = null;

    public function getCode(): int
    {
        return $this->code;
    }

    public function setCode(int $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getSubKantarCode(): ?int
    {
        return $this->subKantarCode;
    }

    public function setSubKantarCode(?int $subKantarCode): self
    {
        $this->subKantarCode = $subKantarCode;

        return $this;
    }

    public function getSubKantarLabel(): ?string
    {
        return $this->subKantarLabel;
    }

    public function setSubKantarLabel(?string $subKantarLabel): self
    {
        $this->subKantarLabel = $subKantarLabel;

        return $this;
    }

    public function getPicbiSociety(): ?PicbiSociety
    {
        return $this->picbiSociety;
    }

    public function setPicbiSociety(?PicbiSociety $picbiSociety): self
    {
        $this->picbiSociety = $picbiSociety;

        return $this;
    }
}
