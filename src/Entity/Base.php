<?php

namespace App\Entity;

use App\Repository\BaseRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: BaseRepository::class)]
#[ORM\Table(name: 'base', schema: 'rogue_one')]
#[ORM\HasLifecycleCallbacks]
class Base extends AbstractEntity
{
    use TimestampableEntityTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\Column(type: 'integer')]
    protected ?int $id;

    #[ORM\Column(type: 'string', length: 100, nullable: false)]
    protected string $label;

    #[ORM\Column(name: 'main_domain', type: 'string', length: 100, nullable: true)]
    protected ?string $mainDomain = null;

    #[ORM\Column(name: 'sending_domain', type: 'string', length: 100, nullable: true)]
    protected ?string $sendingDomain = null;

    #[ORM\Column(name: 'sender_email', type: 'string', length: 100, nullable: true)]
    protected ?string $senderEmail = null;

    #[ORM\Column(name: 'image_domain', type: 'string', length: 100, nullable: true)]
    protected ?string $imageDomain = null;

    #[ORM\Column(type: 'boolean', nullable: false, options: ['default' => false])]
    protected bool $active = false;

    #[ORM\Column(type: 'string', length: 100, nullable: true)]
    protected ?string $category = null;

    #[ORM\Column(type: 'string', length: 100, nullable: true)]
    protected ?string $router = null;

    #[ORM\Column(name: 'restricted_days', type: 'simple_array', nullable: true)]
    protected ?array $restrictedDays = null;

    #[ORM\Column(name: 'auto_creation_days', type: 'simple_array', nullable: true)]
    protected ?array $autoCreationDays = null;

    #[ORM\Column(name: 'auto_validation_days', type: 'simple_array', nullable: true)]
    protected ?array $autoValidationDays = null;

    #[ORM\Column(name: 'router_url', type: 'string', length: 100, nullable: true)]
    protected ?string $routerUrl = null;

    #[ORM\Column(name: 'reply_to', type: 'string', length: 100, nullable: true)]
    protected ?string $replyTo = null;

    #[ORM\Column(name: 'consent_ref', type: 'integer', nullable: true)]
    protected ?int $consentRef = null;

    #[ORM\Column(name: 'shoot_hour', type: 'string', length: 5, nullable: true)]
    protected ?string $shootHour = null;

    #[ORM\Column(name: 'max_emails_per_day', type: 'integer', nullable: true, options: ['default' => 1])]
    protected int $maxEmailsPerDay = 1;

    #[ORM\Column(type: 'string', length: 100, nullable: false)]
    protected string $frequency;

    #[ORM\ManyToOne(targetEntity: Editor::class)]
    #[ORM\JoinColumn(name: 'editor_id', referencedColumnName: 'id', nullable: false)]
    protected ?Editor $editor = null;

    #[ORM\OneToOne(targetEntity: Template::class)]
    #[ORM\JoinColumn(name: 'default_template_id', referencedColumnName: 'id')]
    protected ?Template $defaultTemplate = null;

    public function getLabel(): string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getMainDomain(): ?string
    {
        return $this->mainDomain;
    }

    public function setMainDomain(?string $mainDomain): self
    {
        $this->mainDomain = $mainDomain;

        return $this;
    }

    public function getSendingDomain(): ?string
    {
        return $this->sendingDomain;
    }

    public function setSendingDomain(?string $sendingDomain): self
    {
        $this->sendingDomain = $sendingDomain;

        return $this;
    }

    public function getSenderEmail(): ?string
    {
        return $this->senderEmail;
    }

    public function setSenderEmail(?string $senderEmail): self
    {
        $this->senderEmail = $senderEmail;

        return $this;
    }

    public function getImageDomain(): ?string
    {
        return $this->imageDomain;
    }

    public function setImageDomain(?string $imageDomain): self
    {
        $this->imageDomain = $imageDomain;

        return $this;
    }

    public function isActive(): bool
    {
        return $this->active ?? false;
    }

    public function getActive(): bool
    {
        return $this->isActive();
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(?string $category): self
    {
        $this->category = $category;

        return $this;
    }

    public function getRouter(): ?string
    {
        return $this->router;
    }

    public function setRouter(?string $router): self
    {
        $this->router = $router;

        return $this;
    }

    public function getRestrictedDays(): ?array
    {
        return $this->restrictedDays;
    }

    public function setRestrictedDays(?array $restrictedDays): self
    {
        $this->restrictedDays = $restrictedDays;

        return $this;
    }

    public function getAutoCreationDays(): ?array
    {
        return $this->autoCreationDays;
    }

    public function setAutoCreationDays(?array $autoCreationDays): self
    {
        $this->autoCreationDays = $autoCreationDays;

        return $this;
    }

    public function getAutoValidationDays(): ?array
    {
        return $this->autoValidationDays;
    }

    public function setAutoValidationDays(?array $autoValidationDays): self
    {
        $this->autoValidationDays = $autoValidationDays;

        return $this;
    }

    public function getRouterUrl(): ?string
    {
        return $this->routerUrl;
    }

    public function setRouterUrl(?string $routerUrl): self
    {
        $this->routerUrl = $routerUrl;

        return $this;
    }

    public function getReplyTo(): ?string
    {
        return $this->replyTo;
    }

    public function setReplyTo(?string $replyTo): self
    {
        $this->replyTo = $replyTo;

        return $this;
    }

    public function getConsentRef(): ?int
    {
        return $this->consentRef;
    }

    public function setConsentRef(?int $consentRef): self
    {
        $this->consentRef = $consentRef;

        return $this;
    }

    public function getShootHour(): ?string
    {
        return $this->shootHour;
    }

    public function setShootHour(?string $shootHour): self
    {
        $this->shootHour = $shootHour;

        return $this;
    }

    public function getMaxEmailsPerDay(): int
    {
        return $this->maxEmailsPerDay;
    }

    public function setMaxEmailsPerDay(int $maxEmailsPerDay): self
    {
        $this->maxEmailsPerDay = $maxEmailsPerDay;

        return $this;
    }

    public function getFrequency(): string
    {
        return $this->frequency;
    }

    public function setFrequency(string $frequency): self
    {
        $this->frequency = $frequency;

        return $this;
    }

    public function getEditor(): ?Editor
    {
        return $this->editor;
    }

    public function setEditor(Editor $editor): self
    {
        $this->editor = $editor;

        return $this;
    }

    public function getEditorName(): ?string
    {
        if (null === $this->getEditor()) {
            return null;
        }

        return $this->getEditor()->getName() ?: null;
    }

    public function getDefaultTemplate(): ?Template
    {
        return $this->defaultTemplate;
    }

    public function setDefaultTemplate(Template $template): self
    {
        $this->defaultTemplate = $template;

        return $this;
    }

    public function getDefaultTemplateName(): ?string
    {
        if (null === $this->getDefaultTemplate()) {
            return null;
        }

        return $this->getDefaultTemplate()->getName() ?: null;
    }
}
