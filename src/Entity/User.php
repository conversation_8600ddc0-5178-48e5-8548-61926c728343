<?php

namespace App\Entity;

use App\Repository\UserRepository;
use Doctrine\ORM\Mapping as ORM;
use ItData\UserBundle\Model\User as UserAbstract;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: 'user', schema: 'rogue_one')]
class User extends UserAbstract implements \Stringable
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\Column(type: 'integer', nullable: false, options: ['unsigned' => false])]
    protected ?int $id;

    #[ORM\Column(type: 'string', length: 50, nullable: true, options: ['fixed' => false])]
    protected ?string $name = null;

    #[ORM\Column(type: 'string', length: 50, nullable: false, options: ['fixed' => false])]
    #[Assert\NotBlank()]
    protected ?string $login = null;

    #[ORM\Column(type: 'string', length: 120, nullable: true, options: ['fixed' => false])]
    protected ?string $email = null;

    #[ORM\Column(type: 'string', length: 100, nullable: true, options: ['fixed' => false])]
    protected ?string $password = null;

    #[ORM\Column(name: 'last_login_date', type: 'datetimetz', nullable: true)]
    protected ?\DateTimeInterface $lastLoginDate;

    #[ORM\Column(type: 'string', length: 50, nullable: true, options: ['fixed' => false])]
    protected ?string $role;

    #[ORM\Column(type: 'string', nullable: true, options: ['fixed' => false])]
    protected ?string $picture;

    public function __call($name, $arguments): string
    {
        return $this->getUserIdentifier();
    }

    public function __toString(): string
    {
        return (string) $this->getLogin();
    }

    public function __serialize(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'login' => $this->login,
            'email' => $this->email,
            'password' => $this->password,
        ];
    }

    public function getRoles(): array
    {
        return ['ROLE_VIEWER'];
    }

    public function getSalt()
    {
        // TODO: Implement getSalt() method.
    }

    public function eraseCredentials(): void
    {
        // TODO: Implement eraseCredentials() method.
    }

    public function getUsername(): string
    {
        return $this->getLogin();
    }

    public function getUserIdentifier(): string
    {
        return (string) $this->getId();
    }

    public function hasId(): bool
    {
        return (bool) $this->getId();
    }
}
