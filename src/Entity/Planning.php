<?php

namespace App\Entity;

use App\Repository\PlanningRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: PlanningRepository::class)]
#[ORM\Table(name: 'planning', schema: 'rogue_one')]
class Planning extends AbstractEntity implements EntityDateInterface
{
    use TimestampableEntityTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\Column(type: 'integer', nullable: false)]
    protected ?int $id = null;

    #[ORM\Column(type: 'string', length: 100, unique: true, nullable: false)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    protected string $name;

    #[ORM\ManyToMany(targetEntity: Base::class)]
    #[ORM\JoinTable(
        name: 'plannings_bases',
        schema: 'rogue_one',
        joinColumns: [new ORM\JoinColumn(name: 'planning_id', referencedColumnName: 'id')],
        inverseJoinColumns: [new ORM\JoinColumn(name: 'base_id', referencedColumnName: 'id')]
    )]
    protected Collection $bases;

    public function __construct()
    {
        $this->bases = new ArrayCollection();
        $this->setCreateDate(new \DateTime());
        $this->setUpdateDate(new \DateTime());
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getBases(): Collection
    {
        return $this->bases;
    }

    public function addBase(Base $shootBase): self
    {
        if (!$this->bases->contains($shootBase)) {
            $this->bases->add($shootBase);
        }

        return $this;
    }

    public function removeBase(Base $shootBase): self
    {
        $this->bases->removeElement($shootBase);

        return $this;
    }
}
