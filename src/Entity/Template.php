<?php

namespace App\Entity;

use App\Repository\TemplateRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: TemplateRepository::class)]
#[ORM\Table(name: 'rogue_one.template')]
class Template extends AbstractContent implements EntityDateInterface
{
    use TimestampableEntityTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'SEQUENCE')]
    #[ORM\Column(type: 'integer', nullable: false)]
    protected ?int $id;

    #[ORM\Column(type: 'simple_array', nullable: true)]
    protected ?array $tags = [];

    #[ORM\Column(type: 'text', nullable: true)]
    protected ?string $content;

    #[ORM\Column(type: 'string', nullable: false)]
    private string $name;

    #[ORM\Column(type: 'boolean', nullable: false)]
    private ?bool $restricted = false;

    private array $allowedShootBases = [];

    #[ORM\Column(type: 'boolean', nullable: false, name: 'used')]
    private bool $isUsed = false;

    #[ORM\Column(type: 'datetime', nullable: false)]
    private \DateTime $lastPullDate;

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(?string $name = null): self
    {
        $this->name = $name;

        return $this;
    }

    public function getRestricted(): bool
    {
        return $this->restricted;
    }

    public function isRestricted(): bool
    {
        return $this->restricted;
    }

    public function setRestricted(bool $restricted = false): self
    {
        $this->restricted = $restricted;

        return $this;
    }

    public function getAllowedShootBases(): array
    {
        return $this->allowedShootBases;
    }

    public function setAllowedShootBases(array $allowedShootBases): self
    {
        $this->allowedShootBases = $allowedShootBases;

        return $this;
    }

    public function isUsed(): bool
    {
        return $this->isUsed;
    }

    public function getUsed(): bool
    {
        return $this->isUsed;
    }

    public function setUsed(bool $isUsed = false): self
    {
        $this->isUsed = $isUsed;

        return $this;
    }

    public function getLastPullDate(): ?\DateTime
    {
        return $this->lastPullDate;
    }

    public function setLastPullDate(?\DateTime $lastPullDate = null): self
    {
        $this->lastPullDate = $lastPullDate;

        return $this;
    }
}
