<?php

namespace App\DTO;

use App\Entity\PicbiContact;

readonly class PicbiContactDTO implements \JsonSerializable
{
    public function __construct(
        public int $code,
        public ?string $lastname,
        public ?string $firstname,
        public ?string $mail,
        public string $fullName,
        public string $fullContact,
    ) {
    }

    public static function fromEntity(PicbiContact $contact): self
    {
        $fullName = trim(($contact->getFirstname() ?? '') . ' ' . ($contact->getLastname() ?? ''));
        $fullContact = $fullName . ($contact->getMail() ? ' <' . $contact->getMail() . '>' : '');
        
        return new self(
            code: $contact->getCode(),
            lastname: $contact->getLastname(),
            firstname: $contact->getFirstname(),
            mail: $contact->getMail(),
            fullName: $fullName,
            fullContact: $fullContact,
        );
    }

    /**
     * @param PicbiContact[] $contacts
     * @return self[]
     */
    public static function fromEntities(array $contacts): array
    {
        return array_map([self::class, 'fromEntity'], $contacts);
    }

    public function jsonSerialize(): array
    {
        return [
            'code' => $this->code,
            'lastname' => $this->lastname,
            'firstname' => $this->firstname,
            'mail' => $this->mail,
            'full_name' => $this->fullName,
            'full_contact' => $this->fullContact,
        ];
    }
}
