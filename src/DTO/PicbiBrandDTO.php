<?php

namespace App\DTO;

use App\Entity\PicbiBrand;

readonly class PicbiBrandDTO implements \JsonSerializable
{
    public function __construct(
        public int $code,
        public string $name,
        public ?int $subKantarCode,
        public ?string $subKantarLabel,
    ) {
    }

    public static function fromEntity(PicbiBrand $brand): self
    {
        return new self(
            code: $brand->getCode(),
            name: $brand->getName(),
            subKantarCode: $brand->getSubKantarCode(),
            subKantarLabel: $brand->getSubKantarLabel(),
        );
    }

    /**
     * @param PicbiBrand[] $brands
     * @return self[]
     */
    public static function fromEntities(array $brands): array
    {
        return array_map([self::class, 'fromEntity'], $brands);
    }

    public function jsonSerialize(): array
    {
        return [
            'code' => $this->code,
            'name' => $this->name,
            'sub_kantar_code' => $this->subKantarCode,
            'sub_kantar_label' => $this->subKantarLabel,
        ];
    }
}
