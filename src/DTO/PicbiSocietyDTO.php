<?php

namespace App\DTO;

use App\Entity\PicbiSociety;

readonly class PicbiSocietyDTO implements \JsonSerializable
{
    /**
     * @param PicbiContactDTO[] $contacts
     * @param PicbiBrandDTO[] $brands
     */
    public function __construct(
        public string $name,
        public ?string $type,
        public int $code,
        public array $contacts,
        public array $brands,
        public ?int $kantarCode,
        public ?string $kantarLabel,
        public ?int $id,
        public ?string $createDate,
        public ?string $updateDate,
    ) {
    }

    /**
     * Create a DTO from a PicbiSociety entity.
     */
    public static function fromEntity(PicbiSociety $society): self
    {
        return new self(
            name: $society->getName() ?? '',
            type: $society->getType(),
            code: $society->getCode(),
            contacts: PicbiContactDTO::fromEntities($society->getContacts()->toArray()),
            brands: PicbiBrandDTO::fromEntities($society->getBrands()->toArray()),
            kantarCode: $society->getKantarCode(),
            kantarLabel: $society->getKantarLabel(),
            id: $society->getId(),
            createDate: $society->getCreateDate()?->format('c'),
            updateDate: $society->getUpdateDate()?->format('c'),
        );
    }

    /**
     * Convert the DTO to an array for JSON serialization.
     */
    public function jsonSerialize(): array
    {
        return [
            'name' => $this->name,
            'type' => $this->type,
            'code' => $this->code,
            'contacts' => $this->contacts,
            'brands' => $this->brands,
            'kantar_code' => $this->kantarCode,
            'kantar_label' => $this->kantarLabel,
            'id' => $this->id,
            'create_date' => $this->createDate,
            'update_date' => $this->updateDate,
        ];
    }

    /**
     * Convert the DTO to an array for JSON serialization.
     * @deprecated Use jsonSerialize() instead
     */
    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    /**
     * Create multiple DTOs from an array of entities.
     *
     * @param PicbiSociety[] $societies
     * @return self[]
     */
    public static function fromEntities(array $societies): array
    {
        return array_map([self::class, 'fromEntity'], $societies);
    }

    /**
     * Convert multiple DTOs to arrays.
     *
     * @param self[] $dtos
     * @return array[]
     */
    public static function toArrays(array $dtos): array
    {
        return array_map(fn(self $dto) => $dto->jsonSerialize(), $dtos);
    }
}
