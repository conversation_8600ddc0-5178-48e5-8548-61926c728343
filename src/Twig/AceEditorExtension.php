<?php

namespace App\Twig;

use Twig\Environment;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

/**
 * Class AceEditorExtension
 *
 * Twig extension for AceEditor functionality
 */
class AceEditorExtension extends AbstractExtension
{
    /**
     * JS Settings for AceEditor
     *
     * @var string
     */
    private $editorSettings = '';

    /**
     * Read-only mode of the editor
     *
     * @var bool
     */
    private $readOnly = false;

    /**
     * @var Environment
     */
    private $twig;

    public function __construct(Environment $twig)
    {
        $this->twig = $twig;
        $this->addSettings("editor.setOption('autoScrollEditorIntoView', true);");
    }

    public function getFunctions(): array
    {
        return [
            new TwigFunction('ace_editor', [$this, 'renderAceEditor'], ['is_safe' => ['html']]),
            new TwigFunction('ace_editor_js', [$this, 'renderAceEditorJS'], ['is_safe' => ['html', 'js']]),
        ];
    }

    public function renderAceEditor(string $id, string $content = '', array $options = []): string
    {
        // Reset settings for each new editor instance
        $this->editorSettings = "editor.setOption('autoScrollEditorIntoView', true);\n";

        if (!empty($options)) {
            $this->setOptions($options);
        }

        return $this->twig->render('ace_editor/editor.html.twig', [
            'id' => $id,
            'value' => $content,
            'javascript' => $this->generateJS($id),
        ]);
    }

    /**
     * Function to render only the JavaScript part
     */
    public function renderAceEditorJS(string $id, array $options = []): string
    {
        // Reset settings for each new editor instance
        $this->editorSettings = "editor.setOption('autoScrollEditorIntoView', true);\n";

        if (!empty($options)) {
            $this->setOptions($options);
        }

        return $this->generateJS($id);
    }

    /**
     * Set multiple options at once
     */
    public function setOptions(array $options): self
    {
        if (isset($options['theme'])) {
            $this->setTheme($options['theme']);
        }
        if (isset($options['mode'])) {
            $this->setMode($options['mode']);
        }
        if (isset($options['tabSize'])) {
            $this->setTabSize($options['tabSize']);
        }
        if (isset($options['softTabs'])) {
            $this->setUseSoftTabs($options['softTabs']);
        }
        if (isset($options['wrapMode'])) {
            $this->setUseWrapMode($options['wrapMode']);
        }
        if (isset($options['fontSize'])) {
            $this->setFontSize($options['fontSize']);
        }
        if (isset($options['readOnly'])) {
            $this->setReadOnly($options['readOnly']);
        }
        if (isset($options['showGutter'])) {
            $this->setShowGutter($options['showGutter']);
        }
        if (isset($options['showPrintMargin'])) {
            $this->setShowPrintMargin($options['showPrintMargin']);
        }
        if (isset($options['highlightActiveLine'])) {
            $this->setHighlightActiveLine($options['highlightActiveLine']);
        }

        return $this;
    }

    /**
     * Set theme
     */
    public function setTheme(string $value): self
    {
        return $this->addSettings("editor.setTheme('ace/theme/$value');");
    }

    /**
     * Set mode
     */
    public function setMode(string $value): self
    {
        return $this->addSettings("editor.session.setMode('ace/mode/$value');");
    }

    /**
     * Set font size
     */
    public function setFontSize(int $value): self
    {
        return $this->addSettings("editor.setFontSize($value);");
    }

    /**
     * Set tab size
     */
    public function setTabSize(int $value): self
    {
        return $this->addSettings("editor.getSession().setTabSize($value);");
    }

    /**
     * Set fold style
     */
    public function setFoldStyle(string $value): self
    {
        return $this->addSettings("
            editor.session.setFoldStyle('$value');
            editor.setShowFoldWidgets('$value' !== 'manual');
        ");
    }

    /**
     * Set key binding
     */
    public function setKeyBinding(string $value): self
    {
        return $this->addSettings("editor.setKeyboardHandler('$value');");
    }

    /**
     * Set soft wrap
     *
     * @param string|int $value
     */
    public function setSoftWrap($value): self
    {
        return $this->addSettings("editor.setOption('wrap', '$value');");
    }

    /**
     * Set use wrap mode
     */
    public function setUseWrapMode(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.getSession().setUseWrapMode($boolValue);");
    }

    /**
     * Set selection style
     */
    public function setSelectionStyle(bool $value): self
    {
        $styleValue = $value ? 'line' : 'text';

        return $this->addSettings("editor.setOption('selectionStyle', '$styleValue');");
    }

    /**
     * Set highlight active line
     */
    public function setHighlightActiveLine(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setHighlightActiveLine($boolValue);");
    }

    /**
     * Set show invisibles
     */
    public function setShowInvisibles(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setShowInvisibles($boolValue);");
    }

    /**
     * Set display indent guides
     */
    public function setDisplayIndentGuides(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setDisplayIndentGuides($boolValue);");
    }

    /**
     * Set horizontal scroll bar
     */
    public function setHScrollBar(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setOption('hScrollBarAlwaysVisible', $boolValue);");
    }

    /**
     * Set vertical scroll bar
     */
    public function setVScrollBar(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setOption('vScrollBarAlwaysVisible', $boolValue);");
    }

    /**
     * Set animated scroll
     */
    public function setAnimatedScroll(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setAnimatedScroll($boolValue);");
    }

    /**
     * Set show gutter
     */
    public function setShowGutter(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.renderer.setShowGutter($boolValue);");
    }

    /**
     * Set show print margin
     */
    public function setShowPrintMargin(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.renderer.setShowPrintMargin($boolValue);");
    }

    /**
     * Set use soft tabs
     */
    public function setUseSoftTabs(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.session.setUseSoftTabs($boolValue);");
    }

    /**
     * Set highlight selected word
     */
    public function setHighlightSelectedWord(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setHighlightSelectedWord($boolValue);");
    }

    /**
     * Set behaviours enabled
     */
    public function setBehavioursEnabled(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setBehavioursEnabled($boolValue);");
    }

    /**
     * Set fade fold widgets
     */
    public function setFadeFoldWidgets(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setFadeFoldWidgets($boolValue);");
    }

    /**
     * Set use elastic tabstops
     */
    public function setUseElasticTabstops(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setOption('useElasticTabstops', $boolValue);");
    }

    /**
     * Set use incremental search
     */
    public function setUseIncrementalSearch(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setOption('useIncrementalSearch', $boolValue);");
    }

    /**
     * Set read only
     */
    public function setReadOnly(bool $value): self
    {
        $this->readOnly = $value;
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setOption('readOnly', $boolValue);");
    }

    /**
     * Set scroll past end
     */
    public function setScrollPastEnd(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setOption('scrollPastEnd', $boolValue);");
    }

    /**
     * Set auto scroll editor into view
     */
    public function setAutoScrollEditorIntoView(bool $value): self
    {
        $boolValue = $this->getBoolValue($value);

        return $this->addSettings("editor.setOption('autoScrollEditorIntoView', $boolValue);");
    }

    /**
     * Set max lines
     */
    public function setMaxLines(int $value): self
    {
        return $this->addSettings("editor.setOption('maxLines', $value);");
    }

    /**
     * Set min lines
     */
    public function setMinLines(int $value): self
    {
        return $this->addSettings("editor.setOption('minLines', $value);");
    }

    /**
     * Add custom editor settings
     */
    public function setEditorSettings(string $str): self
    {
        return $this->addSettings($str);
    }

    public function getName(): string
    {
        return 'ace_editor_extension';
    }

    /**
     * Convert boolean to JavaScript boolean string
     */
    protected function getBoolValue(bool $value): string
    {
        return $value ? 'true' : 'false';
    }

    /**
     * Add settings to the editor settings string
     */
    protected function addSettings(string $str): self
    {
        $this->editorSettings .= "$str\n";

        return $this;
    }

    /**
     * Generate JavaScript code for the editor
     */
    protected function generateJS(string $id): string
    {
        return <<<JS
<script>
(function ($) {
    window.editor = ace.edit('$id');
    
    // disable warning message about scrolling
    editor.\$blockScrolling = Infinity;
    
    // set settings
    {$this->editorSettings}
    
    // call our js module if it exists
    if (typeof editorApp !== 'undefined') {
        editorApp.initialize(editor, '$id');
    }
}(jQuery));
</script>
JS;
    }
}
