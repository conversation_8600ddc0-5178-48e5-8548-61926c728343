<?php

namespace App\Twig;

use Symfony\Component\HttpFoundation\RequestStack;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class NavbarExtension extends AbstractExtension
{
    public function __construct(private readonly RequestStack $request)
    {
    }

    public function getFunctions(): array
    {
        return [
            new TwigFunction('isCurrentRouteActive', [$this, 'isCurrentRouteActive']),
        ];
    }

    public function isCurrentRouteActive(string $routePrefix)
    {
        $currentRouteName = $this->request->getCurrentRequest()->attributes->get('_route');

        return str_starts_with((string) $currentRouteName, $routePrefix);
    }
}
