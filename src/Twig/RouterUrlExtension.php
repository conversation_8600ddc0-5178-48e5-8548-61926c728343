<?php

namespace App\Twig;

use Symfony\Component\HttpFoundation\RequestStack;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class RouterUrlExtension extends AbstractExtension
{
    public function __construct(
        private readonly RequestStack $request,
        private readonly string $rootDomain,
    ) {
    }

    public function getFunctions(): array
    {
        return [
            new TwigFunction('router_urls', [$this, 'getRouterUrls']),
        ];
    }

    public function getRouterUrls(string $publicRef): array
    {
        $baseUri = '/get-nl/'.$publicRef;

        return [
            '' => [
                'router' => $this->getScheme().'://rogue-one.prismadata.fr'.$baseUri,
                'router_noad' => $this->getScheme().'://rogue-one.prismadata.fr'.$baseUri.'?noAds=1',
            ],
            'Splio' => [
                'router' => $this->getScheme().'://'.$this->rootDomain.$baseUri,
                'router_noad' => $this->getScheme().'://'.$this->rootDomain.$baseUri.'?noAds=1',
            ],
            'Webrivage' => [
                'router' => $this->getScheme().'://'.$this->rootDomain.$baseUri.'?partner=WR',
                'router_noad' => $this->getScheme().'://'.$this->rootDomain.$baseUri.'?noAds=1&partner=WR',
            ],
        ];
    }

    private function getScheme(): string
    {
        return $this->request->getCurrentRequest()->getScheme();
    }
}
