<?php

namespace App\Twig;

use App\Service\PlanningService;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class PlanningExtension extends AbstractExtension
{
    public function __construct(
        protected readonly PlanningService $planningService
    ) {
    }

    public function getFunctions(): array
    {
        return [
            new TwigFunction('get_plannings', [$this, 'getPlannings']),
        ];
    }

    public function getPlannings(): array
    {
        return $this->planningService->getList();
    }
}
