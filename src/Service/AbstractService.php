<?php

namespace App\Service;

use App\Entity\EntityInterface;
use App\Repository\AbstractRepository;

abstract class AbstractService
{
    public function __construct(
        protected readonly AbstractRepository $repository
    ) {
    }

    public function getList(array $filter = [], array $sort = [], ?int $limit = null, ?int $offset = null): array
    {
        return $this->repository->findBy($filter, $sort, $limit, $offset);
    }

    public function getOneBy(array $filter): ?EntityInterface
    {
        return $this->repository->findOneBy($filter);
    }

    public function getById(int|string $id): ?EntityInterface
    {
        return $this->repository->find($id);
    }

    public function create(): EntityInterface
    {
        return $this->repository->createEntity();
    }
}
