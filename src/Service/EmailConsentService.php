<?php

namespace App\Service;

use App\Repository\EmailConsentRepository;

class EmailConsentService extends AbstractService
{
    public function __construct(
        protected readonly EmailConsentRepository $emailConsentRepository,
        protected readonly BaseService $baseService,
    ) {
        parent::__construct($emailConsentRepository);
    }

    public function getUnusedConsents(): array
    {
        $usedUrls = $this->getUsedRouterUrls();
        $availableRefs = $this->getAllAvailableRefs();

        return array_values(array_diff($availableRefs, $usedUrls));
    }

    private function getUsedRouterUrls(): array
    {
        $bases = $this->baseService->getList();
        $urls = array_map(fn ($base) => $base->getRouterUrl(), $bases);

        return array_unique(array_filter($urls));
    }

    private function getAllAvailableRefs(): array
    {
        $consents = $this->emailConsentRepository->getConsentList();
        $thematics = $this->emailConsentRepository->getThematicsList();

        $allRefs = array_unique(array_merge($consents, $thematics));
        sort($allRefs);

        return $allRefs;
    }
}
