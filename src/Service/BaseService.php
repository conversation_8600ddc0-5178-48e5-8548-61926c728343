<?php

namespace App\Service;

use App\Event\Base as BaseEvent;
use App\Repository\BaseRepository;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class BaseService extends AbstractModularService
{
    protected ?string $eventClass = BaseEvent::class;

    public function __construct(
        BaseRepository $repository,
        EventDispatcherInterface $eventDispatcher
    ) {
        parent::__construct($repository, $eventDispatcher);
    }

    public function delete(EntityInterface $entity): self
    {
        /** @var Base $event */
        $event = $this->getEvent();
        $event->setEntity($entity);

        // Dispatch delete event to allow subscribers to validate or prevent deletion
        $this->dispatchEvent($event, BaseEvent::EVENT_DELETE);

        // If event propagation was stopped by a subscriber, don't proceed with deletion
        if ($event->isPropagationStopped()) {
            return $this;
        }

        // Proceed with deletion
        $this->repository->deleteOne($entity);

        // Dispatch post-delete event
        $this->dispatchEvent($event, BaseEvent::EVENT_DELETE_POST);

        return $this;
    }
}
