<?php

namespace App\Service;

use App\Repository\PicbiSocietyRepository;

class PicbiSocietyService extends AbstractService
{
    protected array $allowedTypes = [
        'AB',
        'AG',
        'AN',
        'AT',
        'BP',
        'CA',
        'ED',
        'FO',
        'JU',
        'MBB',
        'PA',
        'REP',
    ];

    public function __construct(PicbiSocietyRepository $repository)
    {
        parent::__construct($repository);
    }

    /**
     * Find PicbiSociety entities from keywords and type filter.
     *
     * @param array $filter Array containing 'type' and/or 'keywords' keys
     * @return array
     * @throws \DomainException When an unsupported type is provided
     */
    public function findFromKeywords(array $filter): array
    {
        // Validate type if provided
        if (isset($filter['type']) && !isset($this->allowedTypes[$filter['type']])) {
            throw new \DomainException(sprintf('%s does not support type "%s"', __METHOD__, $filter['type']));
        }

        return $this->repository->findFromKeywords($filter);
    }
}
