<?php

namespace App\Service;

use App\Entity\Editor as EditorEntity;
use App\Entity\EntityInterface;
use App\Repository\EditorRepository;

class EditorService extends AbstractService
{
    public function __construct(EditorRepository $repository)
    {
        parent::__construct($repository);
    }

    public function save(EditorEntity $editor): self
    {
        // @todo handle through Listeners with AbstractModularService if necessary
        if (!$editor->hasId()) {
            $editor->setCreateDate(new \DateTime('now', new \DateTimeZone('utc')));
        }
        $editor->setUpdateDate(new \DateTime('now', new \DateTimeZone('utc')));
        $this->repository->save($editor);

        return $this;
    }

    public function remove(EntityInterface $entity, array $options = []): void
    {
        // @todo delete checks to process
        // parent::remove($entity, $options);
    }
}
