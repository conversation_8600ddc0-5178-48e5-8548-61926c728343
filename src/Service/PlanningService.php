<?php

namespace App\Service;

use App\Entity\Planning;
use App\Repository\PlanningRepository;
use Doctrine\ORM\EntityManagerInterface;

class PlanningService extends AbstractService
{
    public function __construct(
        protected readonly PlanningRepository $planningRepository,
        protected readonly EntityManagerInterface $entityManager
    ) {
        parent::__construct($planningRepository);
    }

    public function getList(array $filter = [], array $sort = [], ?int $limit = null, ?int $offset = null): array
    {
        return $this->planningRepository->findMany([], ['name' => 'ASC']);
    }

    public function delete(Planning $planning): self
    {
        $this->planningRepository->deleteOne($planning);

        return $this;
    }
}
