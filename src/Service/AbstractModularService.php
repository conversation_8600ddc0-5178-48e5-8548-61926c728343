<?php

namespace App\Service;

use App\Event\EventInterface;
use App\Repository\AbstractRepository;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

abstract class AbstractModularService extends AbstractService
{
    protected ?EventInterface $event = null;
    protected ?string $eventClass = null;

    public function __construct(
        AbstractRepository $repository,
        protected readonly EventDispatcherInterface $eventDispatcher
    ) {
        parent::__construct($repository);
    }

    protected function createEvent(): EventInterface
    {
        $class = $this->eventClass;
        $event = new $class();
        $this->event = $event;

        return $event;
    }

    protected function getEvent(): EventInterface
    {
        if (!isset($this->event)) {
            $this->createEvent();
        }

        return $this->event;
    }

    protected function setEvent(EventInterface $event): self
    {
        $this->event = $event;

        return $this;
    }

    protected function dispatchEvent(EventInterface $event, $eventName): object
    {
        try {
            $this->eventDispatcher->dispatch($event, $eventName);
        } catch (\Exception $e) {
            $event->setError(sprintf(
                'Exception in listeners ; %s : %s - %s',
                get_class($e),
                $e->getMessage(),
                $e->getTraceAsString()
            ));
        }

        $error = $event->getError();
        if ($error) {
            throw new \RuntimeException(sprintf('Erreur: %s', $event->getError()));
        }

        return $event;
    }
}
