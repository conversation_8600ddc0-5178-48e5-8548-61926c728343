{% extends 'form.html.twig' %}

{% block title %}{{ parent() }} - Édition du planning {{ planning.name }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
{% endblock %}

{% block formTitle %}
    Édition du planning {{ planning.name }}
{% endblock %}

{% block content %}
    {{ form_start(form) }}
        <div class="row">
            <div class="col-md-6">
                {{ form_row(form.name) }}
            </div>
            <div class="col-md-6">
                {{ form_row(form.bases) }}
            </div>
        </div>
    {{ form_end(form) }}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script src="{{ asset('js/planning.js') }}"></script>
    <script>
        $(document).ready(function () {
            Planning.initSelect2();
        });
    </script>
{% endblock %}