{% extends 'list.html.twig' %}

{% block title %}
    {{ parent() }} Liste des Plannings
{% endblock %}

{% block dataTableTitle %}
    Liste des Plannings
{% endblock %}

{% block actionButtons %}
    <a class="btn btn-outline-success" href="{{ path('planning_create') }}" role="button">
        <i class="fa-solid fa-pen-to-square"></i> Ajouter un nouveau Planning
    </a>
{% endblock %}

{% block dataTable %}
    <div id="planning-list" class="font-small">Loading...</div>
    <div>
        {% embed 'partials/components/_basic_modal.html.twig' with { canClose: true, size: 'modal-lg'} %}
            {% block modalId %}delete-modal{% endblock %}
            {% block modalTitle %}Supression{% endblock %}
            {% block modalBody %}
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer ce Planning ?</p>
                    <p>Cette action est irréversible.</p>
                    <p>Voulez-vous vraiment continuer ?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete">Supprimer</button>
                </div>
            {% endblock %}
        {% endembed %}
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/planning.js') }}"></script>
    <script>
        $(function() {
            const datatable = Planning.initDataTable({{ datatable_settings(datatable) }});
            Planning.initDeleteAction(datatable);
        });
    </script>
{% endblock %}