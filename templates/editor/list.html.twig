{% extends 'list.html.twig' %}

{% block title %}{{ parent() }} - Éditeurs {% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/editor.js') }}"></script>
{% endblock %}

{% block dataTableTitle %}
    Liste des Éditeurs
{% endblock %}

{% block actionButtons %}
    <a class="btn btn-outline-success" href="{{ path('editor_create') }}" role="button">
        <i class="fa-solid fa-pen-to-square"></i> Ajouter un nouvel Éditeur
    </a>
{% endblock %}

{% block dataTable %}
    <div id="editor-list" class="font-small">Loading...</div>
{% endblock %}

{% block customScripts %}
    {{ parent() }}
    <script>
        $(function() {
            Editor.initDataTable({{ datatable_settings(datatable) }});
        });
    </script>
{% endblock %}
