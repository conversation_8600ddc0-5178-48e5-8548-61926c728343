{% extends 'base.html.twig' %}

{% block title %}
    {{ parent() }}
{% endblock %}

{% block body %}
<div id="content" class="{{ formContainerClass|default('container') }}">
    <h3 class="margin-top-sm">{% block formTitle %}{% endblock %}</h3>
    <div class="row">
        {% block content %}
            {% for child in form %}
                {% if child.vars.name not in ['cancel', 'save'] %}
                    {{ form_row(child) }}
                {% endif %}
            {% endfor %}
            <div class="d-flex gap-2 justify-content-end mt-3">
                {{ form_row(form.cancel) }}
                {{ form_row(form.save) }}
            </div>
        {% endblock %}
    </div>
    <div id="footer">
        {% block footer %}
        {% endblock %}
    </div>
</div>
{% endblock %}
