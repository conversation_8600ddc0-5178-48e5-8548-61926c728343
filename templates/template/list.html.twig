{% extends 'list.html.twig' %}

{% block title %}
    {{ parent() }} - Liste des Templates
{% endblock %}

{% block javascripts %}
        {{ parent() }}
        <script src="{{ asset('js/template.js') }}"></script>
{% endblock %}

{% block dataTableTitle %}
    Liste des Templates
{% endblock %}

{% block actionButtons %}
    <a class="btn btn-outline-success" href="{{ path('template_create') }}" role="button">
        <i class="fa-solid fa-pen-to-square"></i> Ajouter un nouveau Template
    </a>
{% endblock %}

{% block dataTable %}
    <div id="template-list" class="font-small">Loading...</div>
    <div>
        {% embed 'partials/components/_basic_modal.html.twig' with { canClose: true, size: 'modal-lg'} %}
            {% block modalId %}delete-modal{% endblock %}
            {% block modalTitle %}Supression{% endblock %}
            {% block modalBody %}
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer ce Template ?</p>
                    <p>Cette action est irréversible.</p>
                    <p>Voulez-vous vraiment continuer ?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete">Supprimer</button>
                </div>
            {% endblock %}
        {% endembed %}
    </div>
    <div >
{% endblock %}
{% block customScripts %}
    {{ parent() }}
    <script>
        $(function() {
             Template.initDataTable({{ datatable_settings(datatable) }});
        });
    </script>
{% endblock %}