{% set type = 'template' %}
{% extends 'editor_base.html.twig' %}

{% block title %}
    {{ parent() }} - Contenu d'un Template
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script src="{{ asset('js/template.js') }}"></script>
{% endblock %}

{% block body %}
    <div class="editor-container">
        {{ ace_editor('editor', template.content ?? '', {
            'theme': 'chrome',
            'mode': 'html',
            'fontSize': 14,
            'readOnly': false,
        }) }}
    </div>

    {{ form_start(form) }}
        {{ form_widget(form.tags, {'id': 'tags'}) }}
        {{ form_widget(form.content, {'id': 'editor-content'}) }}
    {{ form_end(form) }}
{% endblock %}

{% block customScripts %}
    {{ parent() }}
    <script>
        $(function() {
            Template.initHtmlEditor(
                '{{ path('template_list') }}',
                '{{ path('api_template_update_content', {'id': template.id}) }}'
            );
        });
    </script>
{% endblock %}
