<div class="col-12 collapsable-cards">
{{ form_start(form) }}
    <div class="row">
        <div class="card mb-4 p-0">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <a data-bs-toggle="collapse" href="#base-data" role="button" aria-expanded="true" aria-controls="base-data">
                        Informations
                        <i class="fa-solid fa-chevron-down float-end"></i>
                    </a>
                </h5>
            </div>
            <div id="base-data" class="collapse show">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            {{ form_row(form.label) }}
                            {{ form_row(form.defaultTemplate) }}
                            {{ form_row(form.routerUrl) }}
                            {{ form_row(form.active) }}
                            {{ form_row(form.shootHour) }}
                        </div>
                        <div class="col-md-4 mb-4">
                            {{ form_row(form.category) }}
                            {{ form_row(form.editor) }}
                            {{ form_row(form.router) }}
                            {{ form_row(form.consentRef) }}
                        </div>
                        <div class="col-md-4 mb-4">
                            {{ form_row(form.mainDomain) }}
                            {{ form_row(form.sendingDomain) }}
                            {{ form_row(form.senderEmail) }}
                            {{ form_row(form.imageDomain) }}
                            {{ form_row(form.replyTo) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% set entity = form.vars.data %}
    <div class="row">
        <div class="card mb-4 p-0">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <a data-bs-toggle="collapse" href="#nl-data" role="button" aria-expanded="true" aria-controls="nl-data">
                        Programmation Newsletters
                        <i class="fa-solid fa-chevron-down float-end"></i>
                    </a>
                </h5>
            </div>
            <div id="nl-data" class="collapse show">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            {{ form_row(form.frequency) }}
                        </div>
                        <div class="col-md-8 mb-4">
                            <div class="mb-3 row align-items-center">
                                <label class="col-2 col-form-label text-end" for="restrictedDays">
                                    Jours de publication
                                    <a href="#" data-bs-toggle="tooltip" data-bs-placement="bottom" title="création et déplacement manuelle autorisés sur le planning">
                                        <i class="fa-solid fa-circle-info"></i>
                                    </a>
                                </label>
                                <div class="col-10">
                                    <div class="d-flex gap-3 flex-wrap">
                                        {% for child in form.restrictedDays %}
                                            {{ form_widget(child) }}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3 row align-items-center">
                                <label class="col-2 col-form-label text-end" for="autoCreationDays">
                                    Jours de création
                                    <a href="#" data-bs-toggle="tooltip" data-bs-placement="bottom" title="tous les matins à 2h00">
                                        <i class="fa-solid fa-circle-info"></i>
                                    </a>
                                </label>
                                <div class="col-10">
                                    <div class="d-flex gap-3 flex-wrap">
                                        {% for child in form.autoCreationDays %}
                                            {{ form_widget(child) }}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3 row align-items-center">
                                <label class="col-2 col-form-label text-end" for="autoValidationDays">
                                    Jours de validation
                                    <a href="#" data-bs-toggle="tooltip" data-bs-placement="bottom" title="tous les matins à 5h00">
                                        <i class="fa-solid fa-circle-info"></i>
                                    </a>
                                </label>
                                <div class="col-10">
                                    <div class="d-flex gap-3 flex-wrap">
                                        {% for child in form.autoValidationDays %}
                                            {{ form_widget(child) }}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3 row align-items-center">
                                <label class="col-2 col-form-label text-end" for="{{ form.maxEmailsPerDay.vars.id }}">
                                    {{ form_label(form.maxEmailsPerDay) }}
                                </label>
                                <div class="col-10">
                                    {{ form_widget(form.maxEmailsPerDay) }}
                                    {{ form_errors(form.maxEmailsPerDay) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% if entity and entity.category == 'NEWSLETTER' and entity.routerUrl is not null %}
    <div class="row">
        <div class="card mb-4 p-0">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <a data-bs-toggle="collapse" href="#router-url-data" role="button" aria-expanded="true" aria-controls="router-url-data">
                        URL Router
                        <i class="fa-solid fa-chevron-down float-end"></i>
                    </a>
                </h5>
            </div>
            <div id="router-url-data" class="collapse show">
                <div class="card-body">
                    {% set routerUrls = router_urls(entity.routerUrl) %}
                    {% for name, router in routerUrls %}
                        {% if name is not empty %}
                            <strong>{{ name }} :</strong>
                        {% endif %}
                        <ul>
                        {% for key, url in router %}
                            <li>URL router&nbsp;
                            {% if key == 'router_noad' %}
                                 sans pub
                            {% endif %}
                                : {{ url }}
                            </li>
                        {% endfor %}
                        </ul>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
{% endif %}
<div class="row">
    <div class="d-flex gap-2 mt-3 justify-content-end">
        {{ form_row(form.cancel) }}
        {{ form_row(form.save) }}
    </div>
</div>
{{ form_end(form) }}
</div>