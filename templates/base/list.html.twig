{% extends 'list.html.twig' %}

{% block title %}
    {{ parent() }} Liste des Bases
{% endblock %}

{% block dataTableTitle %}
    Liste des Bases
{% endblock %}

{% block actionButtons %}
    <a class="btn btn-outline-success" href="{{ path('base_create') }}" role="button">
        <i class="fa-solid fa-pen-to-square"></i> Ajouter une nouvelle Base
    </a>
{% endblock %}

{% block dataTable %}
    <div id="base-list" class="font-small">Loading...</div>
    <div>
        {% embed 'partials/components/_basic_modal.html.twig' with { canClose: true, size: 'modal-lg'} %}
            {% block modalId %}delete-modal{% endblock %}
            {% block modalTitle %}Supression{% endblock %}
            {% block modalBody %}
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer cette Base ?</p>
                    <p>Cette action est irréversible.</p>
                    <p>Voulez-vous vraiment continuer ?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete">Supprimer</button>
                </div>
            {% endblock %}
        {% endembed %}
    </div>
    <div class="modal fade" id="router-modal" tabindex="-1" aria-labelledby="routerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="routerModalLabel">URL routeur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="router-urls-container">
                        <!-- Content will be populated dynamically -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script src="{{ asset('js/base.js') }}"></script>
<script>
    $(function() {
        // Initialize DataTable
        const datatable = Base.initDataTable({{ datatable_settings(datatable) }});
        // Initialize Router Modal
        Base.initRouterModal('{{ path('api_router_urls') }}');
        // Initialize Delete Action
        Base.initDeleteAction(datatable);
    });
</script>
{% endblock %}
