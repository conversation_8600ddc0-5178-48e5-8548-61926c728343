<div class="btn-group" role="group">
    {% if row.routerUrl is not null %}
        <button type="button" class="btn btn-sm btn-outline-info"
                data-bs-toggle="modal"
                data-bs-target="#router-modal"
                data-router-url="{{ row.routerUrl }}">
            <i class="fa fa-link"></i>
        </button>
    {% else %}
        <button type="button" class="btn btn-sm btn-outline-warning" disabled>
            <i class="fa fa-link"></i>
        </button>
    {% endif %}

    <a href="{{ path('base_update', {'id': row.id}) }}" class="btn btn-sm btn-outline-primary">
        <i class="fa fa-edit"></i>
    </a>

    <button type="button" class="btn btn-sm btn-outline-danger"
            data-bs-toggle="modal"
            data-bs-target="#delete-modal"
            data-url="{{ path('base_delete', {'id': row.id}) }}">
        <i class="fa fa-trash"></i>
    </button>
</div>
