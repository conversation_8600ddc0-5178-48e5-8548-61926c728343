{% use "bootstrap_5_horizontal_layout.html.twig" %}

{% block form_label_class -%}
    col-sm-2 col-form-label-sm
{%- endblock form_label_class %}

{% block form_group_class -%}
    col-sm-10 form-control-sm
{%- endblock form_group_class %}


{% block form_widget_simple -%}
    {%- if type is not defined or type != 'hidden' %}
        {%- set widget_class = ' form-control form-control-sm' %}
        {%- if type|default('') == 'color' -%}
            {%- set widget_class = widget_class ~ ' form-control-color' -%}
        {%- elseif type|default('') == 'range' -%}
            {%- set widget_class = ' form-range' -%}
        {%- endif -%}
        {%- set attr = attr|merge({class: (attr.class|default('') ~ widget_class)|trim}) -%}
    {% endif -%}
    {%- if type is defined and type in ['range', 'color'] %}
        {# Attribute "required" is not supported #}
        {% set required = false %}
    {% endif -%}
    {{- parent() -}}
{%- endblock form_widget_simple %}


{%- block form_errors -%}
    {%- if errors|length > 0 -%}
        {%- for error in errors -%}
            <div class="{% if form is not rootform %}invalid-feedback{% else %}alert alert-danger{% endif %} d-block">{{ error.message|raw }}</div>
        {%- endfor -%}
    {%- endif %}
{%- endblock form_errors %}