{% if canClose is not defined %}
    {% set canClose = true %}
{% endif %}
{% if size is not defined %}
    {% set size = 'modal-xl' %}
{% endif %}
<!-- formModal {{ block('modalTitle') }} - {{ block('modalId') }} -->
<div class="modal fade" id="{% block modalId %}{{ random() }}{% endblock %}"
     role="dialog" tabindex="-1"
     {% if not canClose %}
         data-bs-backdrop="static" data-bs-keyboard="false"{% endif %}
     aria-hidden="true">
    <div class="modal-dialog {{ size }}">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="formModalLabel">{% block modalTitle %}{% endblock %}</h5>
                {% if canClose %}
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                {% endif %}
            </div>
            <div class="modal-body">
                {% block modalBody %}{% endblock %}
            </div>
        </div>
    </div>
</div>
<div class="modal-loading-template" style="display: none;">
    <div class="modal-loading">
        <div class="spinner-grow text-success" role="status"><span class="sr-only">Loading...</span></div>
    </div>
</div>
<!-- / formModal {{ block('modalTitle') }} - {{ block('modalId') }} -->