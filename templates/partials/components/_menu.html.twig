<nav class="navbar navbar-expand-lg navbar-dark bg-navbar">
    <div class="container-fluid">
        <a class="navbar-brand" href="">
            <img class="logo-lg" src="{{ asset('img/rogue-one-logo.png') }}" alt="Rogue-One" width="36" height="36">
            &nbsp;<span><b>Rogue-One</b></span>
        </a>
        <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div id="navbarCollapse" class="collapse navbar-collapse">
            <ul class="navbar-nav mr-auto">
                <li class="nav-item">
                    <a class="nav-link" href="">
                        <i class="fa-solid fa-calendar" aria-hidden="true"></i>
                        Agenda
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link{% if isCurrentRouteActive('editor_list') %} active{% endif %}" href="{{ path('editor_list') }}">
                        <i class="fa-solid fa-user-tie" aria-hidden="true"></i>
                        Editeurs
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link{% if isCurrentRouteActive('base_list') %} active{% endif %}" href="{{ path('base_list') }}">
                        <i class="fa-solid fa-database" aria-hidden="true"></i>
                        Bases
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link{% if isCurrentRouteActive('template') %} active{% endif %}" href="{{ path('template_list') }}">
                        <i class="fa-solid fa-newspaper" aria-hidden="true"></i>
                        Templates
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle{% if isCurrentRouteActive('planning') %} active{% endif %}" href="#" id="planningDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fa-solid fa-calendar-days" aria-hidden="true"></i>
                        Plannings
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="planningDropdown">
                        <li><a class="dropdown-item planning-link" href="#">Planning Global</a></li>
                        {% set plannings = get_plannings() %}
                        {% if plannings|length > 0 %}
                            {% for planning in plannings %}
                                <li><a class="dropdown-item planning-link" href="#">{{ planning.name }}</a></li>
                            {% endfor %}
                        {% endif %}
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ path('planning_list') }}">Configurer...</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="">
                        <i class="fa-solid fa-envelope" aria-hidden="true"></i>
                        Email Statiques
                    </a>
                </li>
            </ul>
            <ul class="navbar-nav ml-auto navbar-right">
                <li class="nav-item ml-auto">
                    {%  if app.user.picture is not null %}
                        <img class="rounded-circle" alt="40x40" src="{{ app.user.picture }}"
                             data-holder-rendered="true"
                             style="width: 40px; float: left;">
                    {% else %}
                        <div class="rounded-circle"
                             style="float: left; width: 40px; background: white; height: 40px; padding: 8px 10.5px; color: #563E7C;">
                            <i class="fas fa-user"
                               style="font-size: 22px;"></i>
                        </div>
                    {% endif %}

                    <a class="nav-link no-border"
                       style="float: right;">
                        <span class="hidden-md hidden-sm">{{ app.user.name }}</span>
                    </a>
                </li>
                <li class="nav-item ml-auto">
                    <a class="nav-link" href="{{ path('it_data_user_logout') }}">
                        <i class="fas fa-sign-out-alt"></i>
                        <span class="hidden-md hidden-sm">Se déconnecter</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>