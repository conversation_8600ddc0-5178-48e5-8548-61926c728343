<div id="menu" class="header">
    <div class="col-12">
        <div class="dropdown menu-item">
            <button class="btn btn-primary dropdown-toggle btn-sm" type="button" id="dropdownMenuActions" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-list"></i>
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuActions">
                <li><a class="dropdown-item" id="btn-beautify" href="#">Beautify</a></li>
                <li><a class="dropdown-item" id="btn-fold" href="#">Fold</a></li>
                <li><a class="dropdown-item" id="btn-search" href="#">Search & replace</a></li>
                {% if type == 'email' %}
                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#listTemplateModal">Edit Template</a></li>
                {% endif %}
            </ul>
        </div>
        {% if type == 'email' %}
            <form id="fragment-list" class="row g-3 align-items-center">
                <div class="col-auto">
                    <label class="form-label" for="fragment">Fragment</label>
                    <select name="fragment" id="fragment" class="form-select form-select-sm">
                        <option>---</option>
                    </select>
                </div>
            </form>
        {% endif %}
        <div class="dropdown menu-item">
            {% if type == 'template' %}
                <button class="btn-editor-insert-fragment btn btn-primary dropdown-toggle btn-sm d-none d-md-block" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-tag">Insert tag d'insertion</i>
                </button>
                <ul id="tag-insertion" class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-value="$#$CREA_ANNONCEUR$#$">$#$CREA_ANNONCEUR$#$</a></li>
                    <li><a class="dropdown-item" href="#" data-value="$#$BON_PLAN$">$#$BON_PLAN$#$</a></li>
                    <li><a class="dropdown-item" href="#" data-value="$#$MINITEXT_CREA$#$">$#$MINITEXT_CREA$#$</a></li>
                    <li><a class="dropdown-item" href="#" data-value="$#$HABILLAGE$#$">$#$HABILLAGE$#$</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item new-tag" href="#" data-bs-toggle="modal" data-bs-target="#tagModal">Add New</a></li>
                </ul>
            {% endif %}
        </div>
        <div class="row-fluid menu-item">
            <select id="tag-perso" class="form-select form-select-sm insert-value" data-live-search="true">
                <option value="">Tag</option>
                <option value="#$#_AAAA_#$#">#$#_AAAA_#$#</option>
                <option value="#$#_MM_#$#">#$#_MM_#$#</option>
                <option value="#$#_JJ_#$#">#$#_JJ_#$#</option>
                <option value="#$#_AAAA_J+{x}_#$#">#$#_AAAA_J+{x}_#$#</option>
                <option value="#$#_MM_J+{x}_#$#">#$#_MM_J+{x}_#$#</option>
                <option value="#$#_JJ_J+{x}_#$#">#$#_JJ_J+{x}_#$#</option>
                <option value="#$#_HEURE_#$#">#$#_HEURE_#$#</option>
                <option value="#$#_MINUTES_#$#">#$#_MINUTES_#$#</option>
                <option value="#$#_TIMESTAMP_#$#">#$#_TIMESTAMP_#$#</option>
                <option value="#$#_MOIS_#$#">#$#_MOIS_#$#</option>
                <option value="#$#_JOUR_SEMAINE_#$#">#$#_JOUR_SEMAINE_#$#</option>
                <option value="#$#_DATE_HEURE_ENVOI_#$#">#$#_DATE_HEURE_ENVOI_#$#</option>
                <option value="#$#_ID_EDIT_#$#">#$#_ID_EDIT_#$#</option>
                <option value="#$#_URL_DESABO_#$#">#$#_URL_DESABO_#$#</option>
                <option value="#$#_DOMAINE_#$#">#$#_DOMAINE_#$#</option>
                <option value="#$#_SENDER_NAME_#$#">#$#_SENDER_NAME_#$#</option>
                <option value="#$#_SENDER_EMAIL_#$#">#$#_SENDER_EMAIL_#$#</option>
                <option value="#$#_OBJET_#$#">#$#_OBJET_#$#</option>
                <option value="#$#_ACCROCHE_#$#">#$#_ACCROCHE_#$#</option>
                <option value="#$$ELSE$$#">#$$ELSE$$#</option>
                <option value="#$$ENDIF$$#">#$$ENDIF$$#</option>
                <option value="#$$IF$$#">#$$IF$$#</option>
                <option value="#$$THEN$$#">#$$THEN$$#</option>
                <option value="$ADRESSE_1$">$ADRESSE_1$</option>
                <option value="$ADRESSE_2$">$ADRESSE_2$</option>
                <option value="$AGE$">$AGE$</option>
                <option value="$ANNEE_NAISSANCE$">$ANNEE_NAISSANCE$</option>
                <option value="$CIVILITE$">$CIVILITE$</option>
                <option value="$CODE_POSTAL$">$CODE_POSTAL$</option>
                <option value="$CONSENT_ID$">$CONSENT_ID$</option>
                <option value="$DATE$">$DATE$</option>
                <option value="$DATE_NAISSANCE$">$DATE_NAISSANCE$</option>
                <option value="$DEPARTEMENT$">$DEPARTEMENT$</option>
                <option value="$ECIRCLE_MESSAGE$">$ECIRCLE_MESSAGE$</option>
                <option value="$ECIRCLE_SIGNATURE$">$ECIRCLE_SIGNATURE$</option>
                <option value="$ECIRCLE_WEBDOMAIN$">$ECIRCLE_WEBDOMAIN$</option>
                <option value="$EMAIL$">$EMAIL$</option>
                <option value="$EMAIL_MD5$">$EMAIL_MD5$</option>
                <option value="$FROMADDR$">$FROMADDR$</option>
                <option value="$IDENTIFIANT$">$IDENTIFIANT$</option>
                <option value="$ID_TITRE$">$ID_TITRE$</option>
                <option value="$MOBILE$">$MOBILE$</option>
                <option value="$NOM$">$NOM$</option>
                <option value="$NOM_TITRE$">$NOM_TITRE$</option>
                <option value="$PAYS$">$PAYS$</option>
                <option value="$PERSO_1$">$PERSO_1$</option>
                <option value="$PERSO_2$">$PERSO_2$</option>
                <option value="$PERSO_3$">$PERSO_3$</option>
                <option value="$PERSO_4$">$PERSO_4$</option>
                <option value="$PERSO_5$">$PERSO_5$</option>
                <option value="$PERSO_6$">$PERSO_6$</option>
                <option value="$PRENOM$">$PRENOM$</option>
                <option value="$PRISMA_CONSENT_ID$">$PRISMA_CONSENT_ID$</option>
                <option value="$PRISMA_ID$">$PRISMA_ID$</option>
                <option value="$PROFESSION$">$PROFESSION$</option>
                <option value="$ROUTER_ENVOI_ID$">$ROUTER_ENVOI_ID$</option>
                <option value="$SIGNE_ASTROLOGIQUE$">$SIGNE_ASTROLOGIQUE$</option>
                <option value="$TELEPHONE$">$TELEPHONE$</option>
                <option value="$URL_DESABO$">$URL_DESABO$</option>
                <option value="$URL_MIRROIR$">$URL_MIRROIR$</option>
                <option value="$URL_ONLY_MIRROIR$">$URL_ONLY_MIRROIR$</option>
                <option value="$URL_ONLY_MIRROIR_EN$">$URL_ONLY_MIRROIR_EN$</option>
                <option value="$URL_UNSUB$">$URL_UNSUB$</option>
                <option value="$VILLE$">$VILLE$</option>
            </select>
        </div>
        {% if type == 'template' %}
            <div class="row-fluid menu-item">
                <select  id="nlf-url" class="form-select form-select-sm insert-value select2" data-live-search="true">
                    <option value="">NL-Factory Templates</option>
                    {% for label, value in template_urls %}
                        <option value="{{ value }}">{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
        {% endif %}
        {% if type == 'email' %}
            {% if errorsCount > 0 or messagesCount > 0 %}
                <button type="button" class="btn btn-warning btn-sm" data-bs-toggle="modal" data-bs-target="#messagesModal">
                    Messages
                    <span id="errors-count" class="badge bg-danger">{{ errorsCount }}</span>
                    <span id="messages-count" class="badge bg-secondary">{{ messagesCount }}</span>
                </button>
            {% endif %}
            <button id="btn-duplicate" class="btn btn-primary btn-sm" type="button" data-bs-toggle="modal" data-bs-target="#duplicateModal">
                Dupliquer
            </button>
            <a href="">
                <button class="btn btn-primary btn-sm" type="button">
                    Info
                </button>
            </a>
            <a href="" target="_blank">
                <button id="btn-preview" class="btn btn-primary btn-sm" type="button">
                    Aperçu
                </button>
            </a>
        {% endif %}
        <div class="dropdown menu-item menu-settings float-end">
            {% if type == 'email' and description is not null %}
                <span id="messages-count" class="bi bi-info-circle text-warning" data-bs-html="true" data-bs-toggle="tooltip" data-bs-placement="left" title="{{ description }}"> </span>
            {% endif %}

            {% if type == 'email' and label is not null %}
                {{ label.base }} / <span class="truncate"> {{ label.email }}</span>
            {% elseif type == 'template' %}
                {{ template.name }}
            {% endif %}
            <button id="btn-editor-save" class="btn btn-info btn-sm" type="button">
                Save
            </button>
            <a href="#" id="view-mobile" class="menu-item menu-item-icon"><i class="fas fa-mobile-alt"></i></a>
            <a href="#" id="view-desktop" class="menu-item menu-item-icon"><i class="fas fa-desktop"></i></a>
            <a href="#" class="dropdown-toggle" data-bs-toggle="dropdown"><i class="bi bi-gear"></i></a>
            <ul id="theme" class="dropdown-menu dropdown-menu-end">
                {% for theme in [
                    {value: 'chrome', label: 'Chrome', selected: true},
                    {value: 'clouds', label: 'Clouds', selected: false},
                    {value: 'monokai', label: 'Monokai', selected: false},
                    {value: 'twilight', label: 'Twilight', selected: false},
                    {value: 'solarized_dark', label: 'Solarized Dark', selected: false},
                    {value: 'solarized_light', label: 'Solarized Light', selected: false},
                    {value: 'cobalt', label: 'Cobalt', selected: false},
                    {value: 'eclipse', label: 'Eclipse', selected: false},
                    {value: 'idle_fingers', label: 'Idle Fingers', selected: false},
                    {value: 'kr_theme', label: 'Kr Theme', selected: false},
                    {value: 'merbivore', label: 'Merbivore', selected: false},
                    {value: 'merbivore_soft', label: 'Merbivore Soft', selected: false},
                    {value: 'mono_industrial', label: 'Mono Industrial', selected: false},
                    {value: 'pastel_on_dark', label: 'Pastel On Dark', selected: false},
                    {value: 'textmate', label: 'TextMate', selected: false},
                    {value: 'tomorrow', label: 'Tomorrow', selected: false},
                    {value: 'tomorrow_night', label: 'Tomorrow Night', selected: false},
                    {value: 'tomorrow_night_blue', label: 'Tomorrow Night Blue', selected: false},
                    {value: 'tomorrow_night_bright', label: 'Tomorrow Night Bright', selected: false},
                    {value: 'tomorrow_night_eighties', label: 'Tomorrow Night Eighties', selected: false},
                    {value: 'vibrant_ink', label: 'Vibrant Ink', selected: false}
                ] %}
                    <li>
                        <a class="dropdown-item {% if theme.selected %}active{% endif %}"
                           href="#"
                           data-value="{{ theme.value }}">
                            {{ theme.label }}
                            {% if theme.selected %}
                                <i class="bi bi-check float-end"></i>
                            {% endif %}
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>