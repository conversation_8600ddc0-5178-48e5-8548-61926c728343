{% for label, messages in app.flashes %}
    {% for message in messages %}
        <div class="alert alert-{{ label }} alert-dismissible fade show" role="alert">
            {% if label == 'warning' or label == 'danger' %}
                <strong>
                    <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                </strong>
            {% endif %}

            {% if label == 'info' or label == 'success' %}
                <strong>
                    <i class="fa fa-info-circle" aria-hidden="true"></i>
                </strong>
            {% endif %}

            <span> {{ message }}</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endfor %}
<div class="alert alert-js alert-dismissible fade show" role="alert" style="display: none">
    <strong class="alert-danger-icon alert-icon" style="display: none">
        <i class="fa-solid fa-exclamation-triangle"></i>
    </strong>
    <strong class="alert-info-icon alert-icon" style="display: none">
        <i class="fa-solid fa-circle-check"></i>
    </strong>
    <span class="alert-message"></span>
    <button type="button" class="btn-close" onclick="$('.alert-js').hide()"></button>
</div>