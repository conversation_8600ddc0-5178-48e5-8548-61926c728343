SELECT e.id as email_id,
       e.date_envoi as email_date,
       e.campaign_id as base_ref,
       e.campaign_name as base_label,
       IF(e.type = 'nl', 'NEWSLETTER', 'PARTNER') as base_category,
       COALESCE((select name from company where id = e.company_annonceur_id), '') as advertiser_label,
       COALESCE((select name from company where id = e.company_platform_id), '') as platform_label,
       COALESCE((select CONCAT(first_name, ' ', last_name) from contact where id = e.contact_pcp_id), '') as commercial_label,
       COALESCE((select CONCAT(first_name, ' ', last_name) from contact where id = e.integrator_id), '') as integrator_label,

       COALESCE(IF(segment_size_auto > 0, segment_size_auto, segment_size_manual), 0) as sent,
       COALESCE(IF(e.segment_size_auto > 0 AND e.deliverred >0, CAST(e.segment_size_auto AS SIGNED)-CAST(e.deliverred AS SIGNED), 0), 0) as rejected,
       COALESCE(e.deliverred, 0) as delivered,
       COALESCE(e.ouvertures, 0) as open,
       COALESCE(e.ouvertures_uniques, 0) as unique_open,
       COALESCE(e.clics, 0) as click,
       COALESCE(e.clics_uniques, 0) as unique_click,
       COALESCE(e.desabonnements, 0) as unsub,

       COALESCE(e.rem_cpm, 0) as cpm,
       COALESCE(e.rem_clics, 0) as cpc,
       COALESCE(e.rem_leads, 0) as cpl,
       COALESCE(e.rem_ventes, 0) as vente
FROM envois e
WHERE e.date_envoi >= "2017-01-01 00:00:00" AND e.status > 0
INTO OUTFILE '/home/<USER>/patrick/pcp-stats.2018-06-18.csv'
FIELDS TERMINATED BY ';'
  ENCLOSED BY '"'
LINES TERMINATED BY '\n';