SELECT r.envoi_id as number,
       r.date as date,
       IF(e.rem_autre <> 0, 0, SUM(COALESCE(r.clics_uniques, 0))) as click,
       IF(e.rem_autre <> 0, 0, SUM(COALESCE(r.leads, 0))) as cpl,
       IF(e.rem_autre <> 0, 0, SUM(COALESCE(r.ventes, 0))) as vente,
       IF(e.rem_autre <> 0, 0, SUM(COALESCE(r.unites_cpm, 0))) as cpm,
       IF(e.rem_autre  = 0, 0, SUM(COALESCE(r.CA_manuel, 0))) as ca
FROM remuneration_envois r
INNER JOIN envois e ON r.envoi_id = e.id
WHERE e.date_envoi >= "2017-01-01 00:00:00" and status > 0
GROUP BY r.envoi_id,r.date
INTO OUTFILE '/home/<USER>/patrick/pcp-revenue.2018-07-23.csv'
  FIELDS TERMINATED BY ';'
  ENCLOSED BY '"'
  LINES TERMINATED BY '\n';