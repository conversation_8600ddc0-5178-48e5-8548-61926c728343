#!/bin/bash

if [ $# -ne 1 ]; then
  prog=$(basename $0)
  echo "Usage : $prog <event>"
  echo "ex: $prog Email::EVENT_CREATE"
  exit 1
fi

EVENT="$1"
DIR_LISTENERS="$( dirname $0 )/../module/RogueOne/src/RogueOne/Service/Listeners"

IFS="
"
for attach in $( grep -r -E "$EVENT|'[*]'" "$DIR_LISTENERS" | grep -- "->attach" ); do
#echo "$attach"
  # ./bin/../module/RogueOne/src/RogueOne/Service/Listeners/Email/CompileHtml.php:        $events->attach(EmailEvent::EVENT_CREATE,    [$this, 'compileHtml'], 100);
  file=$(echo "$attach" | cut -d":" -f1)
  listener=$( echo "`basename $(dirname "$file")` \\ `basename "$file" | cut -d '.' -f1 `" );
  method=$( echo "$attach" | sed -e "s/^.*\[ *\$this *, *[\"']\([^'\"]*\)[\"'] *\].*$/\1/" )
  priority=$( echo "$attach" | sed -e "s/^.*[^-0-9]\(-\{0,1\}[0-9]*\));.*$/\1/" )
  printf "%5d - %s :: %s\n" "$priority" "$listener" "$method"
done | sort -n -r