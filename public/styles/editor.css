html,
body {
    height: 100%;
    margin: 0;
    background: #fff;
}
label {
    font-weight: normal !important
}
.box-column {
    display: flex;
    flex-flow: column;
    height: 100%;
}
.box-row {
    display: flex;
    flex-flow: row;
    width: 100%;
}
.header {
    flex: 0 1 40px;
}
#menu {
    width:100%;
    height: 40px;
    margin-bottom: 5px;
    padding: 5px 5px 0 5px;
    -webkit-box-shadow: 0 0 5px rgba(57,70,78,.2);
    -moz-box-shadow: 0 0 5px rgba(57,70,78,.2);
    box-shadow: 0 0 5px rgba(57,70,78,.2);
}
#search {
    width:100%;
    height: 40px;
    padding: 5px;
    -webkit-box-shadow: 0 0 5px rgba(57,70,78,.2);
    -moz-box-shadow: 0 0 5px rgba(57,70,78,.2);
    box-shadow: 0 0 5px rgba(57,70,78,.2);
}
#search .width-40 {
    width: 30%;
}
#search .search-input {
    width: 50%;
}
#search .search-input .form-inline .input-group .form-control, .form-inline .input-group .input-group-addon, .form-inline .input-group .input-group-btn {
    width: 10%;
}
.main-container {
    width: 100%;
    height: 100%;
    display: flex;
}
.editor-container {
    width: 100%;
    height: 100%;
    display: flex;
}
#editor, #preview {
    height: 100%;
    width: 50%;
}
#preview {
    border-left: 1px solid #ebebeb;
    background: #e5e5e5;
    /*overflow-x: hidden;*/
}
#preview iframe {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    border: 0;
    display: block;
}
.editor-resize {
    display: flex;
    width: 30px;
}
.editor-resize.left, .editor-resize.right {
    width: 20px;
}
.editor-resize-left, .editor-resize-right {
    -ms-flex-align: center;
    align-items: center;
    background-color: #c2c2c2;
    border-right: 1px solid #b4b4b4;
    color: #747474;
    cursor: pointer;
    display: -ms-flexbox;
    display: flex;
    -ms-flex: 1;
    flex: 1;
    font-size: 8px;
    -ms-flex-pack: center;
    justify-content: center;
    transition: background-color .3s ease-in-out;
}
.btn-circle {
    width: 20px;
    height: 20px;
    text-align: center;
    padding: 2px 0;
    font-size: 10px;
    line-height: 1.42;
    border-radius: 50%;
}
.form-inline {
    display: inline;
}
.menu-item {
    display: inline-block;
}
.menu-item-icon {
    cursor: pointer;
    padding: 0 5px;
}
.menu-settings {
    padding: 0 5px;
}
#theme li a.selected {
    font-weight: bold;
}
.ace_search {
    max-width: 326px !important;
}
.hidden {
    display: none!important;
}
/* ace editor custom */
.readonly-highlight{
    background-color: #595c60;
    opacity: 0.2;
    position: absolute;
}
/** send-bat styles**/
.layout {
    width: 50%;
}
.layout iframe {
    height: 100%;
    margin: 0 auto;
    border: 0;
    display: block;
}
.separator {
    width: 2px;
    background-color: #c2c2c2;
}
.content-container {
    display: flex;
    height: 94.5%;
}
iframe#desktop {
    width: 100%;
}
iframe#mobile {
    width: 320px;
}
/* custom overrides */
.bootstrap-select>.dropdown-toggle.bs-placeholder, .bootstrap-select>.dropdown-toggle.bs-placeholder:active, .bootstrap-select>.dropdown-toggle.bs-placeholder:focus, .bootstrap-select>.dropdown-toggle.bs-placeholder:hover {
    color: #fff;
}
div#duplicateModal ul.typeahead {
    width: 95%;
}
.truncate {
    display: inline-block;
    vertical-align:middle;
    max-width: 400px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
.word-wrap {
    word-wrap: break-word;
}
div#messagesModal .modal-body h5 {
    font-size: 16px;
}
div#messagesModal .modal-body div:first-child h5 {
    margin-top: 0px;
}
div#messagesModal .modal-body pre {
    padding: 1px 5px;
    margin: 0;
}
div#messagesModal .modal-body .alert-danger pre {
    border-right: 1px solid #ff8282;
    border-left: 1px solid #ff8282;
    border-bottom: 1px solid #ff8282;
}
div#messagesModal .modal-body pre:first-child {
    border-top: 1px solid #ff8282;
}
div#messagesModal .modal-body .alert-warning pre {
    border-right: 1px solid #ffc107;
    border-left: 1px solid #ffc107;
    border-bottom: 1px solid #ffc107;
}
div#messagesModal .modal-body .alert-warning pre:first-child {
    border-top: 1px solid #ffc107;
}

.tooltip-inner {
    white-space: pre-wrap;
}
div#messagesModal pre {
    white-space:pre-wrap;
    background: none;
    border: 0;
    border-radius: 0;
    color: inherit;
}

.close {
    color: #8f8f8f;
    opacity: 1;
    padding-top: 20px;
}