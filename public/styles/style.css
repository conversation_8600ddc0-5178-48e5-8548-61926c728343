
.logo-lg {
    color: #FFF;
    margin-left: 25px;
}

.margin-right-xs {
    margin-right: 6px!important;
}

.margin-right-sm {
    margin-right: 15px!important;
}

.margin-right-md {
    margin-right: 25px!important;
}

.margin-top-sm {
    margin-top: 15px!important;
}

.margin-top-md {
    margin-top: 25px!important;
}

.margin-bottom-sm {
    margin-bottom: 15px!important;
}

.margin-bottom-md {
    margin-bottom: 25px!important;
}

.margin-bottom-lg {
    margin-bottom: 45px!important;
}

.margin-left-sm {
    margin-left: 15px!important;
}

.margin-left-md {
    margin-left: 25px!important;
}

.bg-navbar {
    background: #181c1c !important;
}

.navbar-right {
    margin-right: 15px;
    margin-left: auto;
}

.full-container {
    height: 100%!important;
    padding: 45px;
}

.text-right {
    text-align: right!important;
}

.nav-item {
    margin-left: 15px;
}

.bg-main {
    background-color: #181c1c;
}

.selecta-color {
    color: #181c1c;
}

.nav-link.active {
    font-size:18px;
    font-weight: bold;
    border:solid 1px;
    border-radius:5px;
}

.nav-link {
    font-weight: bold;
    border-radius:5px;
    border: 1px solid #181c1c;
}

.no-border {
    border: none !important;
}

.nav-link:hover {
    font-weight: bold;
    border:solid 1px;
    border-radius:5px;
}

.cursor-pointer {
    cursor: pointer;
}

/* Datatable style */
div.dataTables_wrapper div.dataTables_filter,
.dataTables_paginate {
    text-align:  right;
}
.dataTables_wrapper .form-control,
.dataTables_wrapper .form-select {
    width: auto;
    display: inline-block;
}
ul.pagination  {
    float: right;
}
.page-item.active .page-link {
    background-color: #181c1c;
    border-color: #181c1c;
}
.page-link, .page-link:hover {
    color: #181c1c;
}
/* End Datatable style */

/* Select2 Bootstrap5 change colors */
.select2-selection__choice,
.select2-results__option--selected,
.select2-results__option--highlighted,
.select2-results__option:hover {
    background-color:#181c1c!important;
    color:white!important;
}

.select2-container {
    width:100%!important;
}

.draggable-group, .draggable, .sortable .list-group-item {
    cursor: grab;
}
.draggable-item, .draggable-group {
    margin: 3px;
    border-radius: .25rem;
    padding: .75rem 1.25rem;
    box-sizing: border-box;
    border: 1px solid rgba(0,0,0,.125);
}
.sortable li {
    background-color: #fff;
}
.sortable .placeholder {
    height: 40px;
    border: 2px dashed #f8e0b1 !important;
    background-color: #fefcf5 !important;
}
.disabled-zone {
    background-color: #f5f5f5;
    padding: 10px;
    color: #b3b3b3;
    text-align: center;
}
ul.group.sortable {
    min-height: 100px;
}
.sortable-ghost {
    background: #e6e6e6;
    /*opacity: 10;*/
}
ul#sortable-list > li.list-item {
    background-color: #caebf2 !important;
}
ul#sortable-list > li.list-item > ul > li.list-item {
    background-color: #fffae8 !important;
}

a.table-default-action:active  {
    all: unset;
}

a.table-default-action {
    all: unset;
}

a.table-default-action:hover {
    all: unset;
    cursor: pointer;
}

@media only screen and (min-width: 1550px) {
    .consent-list {
        overflow: auto;
        position: fixed;
        top: 210px;
        bottom: 0px;
        min-width: 150px;
        width: 16%;
    }

    .structure {
        overflow: auto;
        position: fixed;
        top: 168px;
        bottom: 0px;
        width: 82%;
    }

    .structure-variables {
        overflow: auto;
        position: fixed;
        top: 168px;
        bottom: 0px;
        left: 10px;
        right: 5px;
        width: 99%;
    }
}


@media only screen and (min-width: 995px) and (max-width: 1550px) {
    .consent-list {
        overflow: auto;
        position: fixed;
        top: 302px;
        bottom: 0px;
        min-width: 150px;
        width: 16%;
    }

    .structure {
        overflow: auto;
        position: fixed;
        top: 260px;
        bottom: 0px;
        width: 82%;
    }

    .structure-variables {
        overflow: auto;
        position: fixed;
        top: 260px;
        bottom: 0px;
        left: 10px;
        right: 5px;
        width: 99%;
    }
}

.alert {
    position: fixed;
    width: 100%;
}

/* Fix Select2 */
.select2-container {
    z-index: 999999;
}

.collapsable-cards a {
    color: #000;
}
.collapsable-cards a[data-bs-toggle="collapse"] i {
    transition: transform 0.2s;
}
.collapsable-cards a[data-bs-toggle="collapse"].collapsed i {
    transform: rotate(-90deg);
}

.dropdown-menu .planning-link {
    padding-left: 1rem;
}

.dropdown-menu .dropdown-divider {
    margin: 0.5rem 0;
}

.nav-item.dropdown .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.25rem;
}

.dropdown-menu {
    max-height: 400px;
    overflow-y: auto;
}
