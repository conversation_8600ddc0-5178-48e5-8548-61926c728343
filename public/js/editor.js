class EditorApp {
    constructor() {
        this.editor = null;
        this.Range = null;
        this.markerId = null;
        this.fragments = [];
    }

    initialize = (editor, id) => {
        this.editor = editor;
        this.Range = ace.require("ace/range").Range;

        // Options de l'éditeur
        this.editor.setOptions({
            enableBasicAutocompletion: true,
            enableLiveAutocompletion: false,
            enableEmmet: true,
            showPrintMargin: false,
        });

        // Initialisation de la valeur
        // let $textarea = $($('#' + id + '-content'));
        // this.editor.getSession().setValue($textarea.val());
        // this.editor.focus();

        // Récupération des fragments
        $("#fragment option").each((_, el) => {
            this.fragments.push($(el).val());
        });

        // Event listeners
        this.editor.on("input", this.preview);
        this.editor.on("click", this.handleToken);
        this.editor.getSession().on("changeScrollTop", this.scrollPreview);

        // Global
        window.editor = this.editor;
        window.Range = this.Range;
    };

    preview = () => {
        let content = this.editor.getValue();
        const tags = JSON.parse($('input#tags').val());
        content = content.replace(/&para/gm, '&amp;para');
        content = this.replaceTags(content, tags);

        const iframe = document.getElementById('result');
        const iframedoc = iframe.contentDocument || iframe.contentWindow.document;
        iframedoc.body.innerHTML = content;
    };

    replaceTags = (content, tags) => {
        for (const [key, value] of Object.entries(tags)) {
            const s = key.replace(/[\{\}\(\)\*\+\?\.\\\^\$\|]/g, "\\$&");
            content = content.replace(new RegExp(s, 'g'), value);
        }
        return content.replace(/<%[^%]+%>/g, '');
    };

    scrollPreview = (scrollOffset) => {
        const iframe = $('#result').contents();
        const editorHeight = this.editor.session.getScreenLength() * this.editor.renderer.lineHeight;
        const scrollRatio = scrollOffset / editorHeight;
        iframe.scrollTop(iframe.height() * scrollRatio);
    };

    highlightToken = (pos, token, cssClass) => {
        const range = new this.Range(pos.row, token.start,
            pos.row, token.start + token.value.length);
        return this.editor.session.addMarker(range, cssClass);
    };

    handleToken = (e) => {
        const pos = e.editor.getCursorPosition();
        const token = e.editor.session.getTokenAt(pos.row, pos.column);

        if (this.markerId !== undefined && this.markerId !== null) {
            this.editor.session.removeMarker(this.markerId);
        }

        if (!token) return;

        if (/\bprisma.\b/.test(token.type)) {
            if (/\bprisma.token.\b/.test(token.type) && this.fragments.includes(token.value)) {
                $('#fragment').val(token.value).change();
            }
            this.markerId = this.highlightToken(pos, token, 'ace_bracket');
        }
    };

    disableRanges = (ranges) => {
        this.editor.clearSelection();
        this.doDisableRanges(ranges);
    };

    doDisableRanges = (ranges) => {
        const session = this.editor.getSession();
        const disabledRanges = ranges.map(range => {
            const disabledRange = new this.Range(range.startRow, range.startColumn, range.endRow, range.endColumn);
            session.addMarker(disabledRange, "readonly-highlight");
            if (range.collapsible === true) {
                session.addFold('...', disabledRange);
            }
            return disabledRange;
        });

        this.editor.keyBinding.addKeyboardHandler({
            handleKeyboard: (data, hash, keyString, keyCode, event) => {
                if (hash === -1 || (keyCode <= 40 && keyCode >= 37)) return false;
                return disabledRanges.some(range => this.intersects(range)) ?
                    {command: "null", passEvent: false} : false;
            }
        });

        const before = (obj, method, wrapper) => {
            const orig = obj[method];
            obj[method] = function() {
                const args = Array.prototype.slice.call(arguments);
                return wrapper.call(this, () => orig.apply(obj, args), args);
            };
            return obj[method];
        };

        const preventReadonly = (next, args) => {
            if (!disabledRanges.some(range => this.intersects(range))) {
                next();
            }
        };

        before(this.editor, 'onPaste', preventReadonly);
        before(this.editor, 'onCut', preventReadonly);

        // Création d'ancres sur les plages désactivées
        disabledRanges.forEach(range => {
            range.start = session.doc.createAnchor(range.start);
            range.end = session.doc.createAnchor(range.end);
            range.$insertRight = true;
        });

        const intersectsRange = (newRange) => {
            return disabledRanges.some(range => newRange.intersects(range));
        };

        const outSideRange = (position) => {
            const {row, column} = position;
            return !disabledRanges.some(range => {
                if (range.start.row < row && range.end.row > row) return true;
                if (range.start.row === row && range.start.column < column) {
                    return range.end.row !== row || range.end.column > column;
                }
                return range.end.row === row && range.end.column > column;
            });
        };

        const old$tryReplace = this.editor.$tryReplace;
        this.editor.$tryReplace = function(range, replacement) {
            return intersectsRange(range) ? null : old$tryReplace.apply(this, arguments);
        };

        const oldInsert = session.insert;
        session.insert = function(position, text) {
            return oldInsert.apply(this, [position, outSideRange(position) ? text : ""]);
        };

        const oldRemove = session.remove;
        session.remove = function(range) {
            return intersectsRange(range) ? false : oldRemove.apply(this, arguments);
        };

        const oldMoveText = session.moveText;
        session.moveText = function(fromRange, toPosition, copy) {
            if (intersectsRange(fromRange) || !outSideRange(toPosition)) return fromRange;
            return oldMoveText.apply(this, arguments);
        };
    };

    intersects = (range) => {
        return this.editor.getSelectionRange().intersects(range);
    };

    beautify = () => {
        const opts = { indent_size: this.editor.getSession().getTabSize() };
        return beautify.beautify(editor.session);
        // return html_beautify(this.editor.session.doc.getValue(), opts);
    };

    beautifySelection = () => {
        const opts = { indent_size: this.editor.getSession().getTabSize() };
        // const result = html_beautify(this.editor.getSelectedText(), opts);
        const result = beautify.beautify(this.editor.getSelectedText(), opts);
        this.editor.session.replace(this.editor.selection.getRange(), result);
        return result;
    };
}

window.editorApp = new EditorApp();
