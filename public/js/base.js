class Base {
    static initDataTable(config) {
        let typeFilterHtml = `<div class="d-inline-block ml-2">
                    <label>Statut de la base : </label>
                    <select id="used-filter" class="form-control-sm">
                        <option value="all">Tous</option>
                        <option value='1'>Active</option>
                        <option value='-1'>Désactivée</option>
                    </select>
                    </div>`;

        let table = $('#base-list').initDataTables(config, {
            searching: true,
            // dom:'<"html5buttons"B>lTfgtpi',
            layout: {
                topStart: 'pageLength',
                topEnd: 'search',
                bottomStart: 'info',
                bottomEnd: 'paging'
            },
            language: datatableLanguage,
            initComplete(settings, json) {
                let datatable = this.api().table();
                $(datatable.container()).find('div.dataTables_filter label:first-child').after(typeFilterHtml);
                // init custom handlers
                initDataTableFilter(datatable);
            }
        });

        function initDataTableFilter(datatable) {
            $('#used-filter').on('change', function () {
                let filterValue = $(this).val();
                if (filterValue === "all") {
                    datatable.column(4).data().search('').draw();
                } else {
                    datatable.column(4).data().search(filterValue).draw();
                }
            });
        }
    }

    static initDeleteAction(datatable) {
        const $deleteModal = $('#delete-modal');
        $deleteModal.on('show.bs.modal', function(e) {
            // set data-url to the delete button
            $(this).find('#confirm-delete').data('url', $(e.relatedTarget).data('url'));
        });

        $deleteModal.find('.btn-danger').on('click', function () {
            const url = $(this).data('url');
            // Blur the button to remove focus before hiding the modal
            this.blur();
            $.ajax({
                url: url,
                type: 'DELETE',
                success: function (result) {
                    alert.success('Suppression effectuée!');
                    datatable.draw();
                },
                error: function (error) {
                    const err = error.responseJSON;
                    let message = err.detail;
                    if (err.more !== undefined && err.more.error !== undefined) {
                        message += '<hr><p>' + err.more.error + '</p>';
                    }
                    alert.error(message);
                },
                complete: function () {
                    $deleteModal.modal('hide');
                }
            });
        });
    }

    static initRouterModal(apiUrl) {
        const $routerModal = $('#router-modal');

        $routerModal.on('show.bs.modal', function(event) {
            const button = $(event.relatedTarget);
            const routerUrl = button.data('router-url');

            if (routerUrl) {
                const $modalBody = $(this).find('.router-urls-container');

                // Clear previous content
                $modalBody.empty();

                // Show loading indicator
                $modalBody.html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Chargement...</div>');

                // Use AJAX to get router URLs
                $.ajax({
                    url: apiUrl,
                    type: 'GET',
                    data: { publicRef: routerUrl },
                    dataType: 'json',
                    success: function(routerUrls) {
                        $modalBody.empty();

                        // Populate modal with URLs
                        $.each(routerUrls, function(name, urls) {
                            const $section = $('<div class="mb-3"></div>');

                            if (name !== '') {
                                $section.append('<h6>' + name + ' : </h6>');
                            }

                            const $list = $('<ul class="list-unstyled"></ul>');

                            $.each(urls, function(type, url) {
                                const label = type === 'router_noad' ? 'URL routeur sans pub : ' : 'URL routeur : ';

                                const $item = $('<li class="mb-2"></li>');
                                const $urlGroup = $('<div class="input-group"></div>');

                                $urlGroup.append('<span class="input-group-text">' + label + '</span>');
                                $urlGroup.append('<input type="text" class="form-control" value="' + url + '" readonly>');
                                $urlGroup.append('<button class="btn btn-outline-primary copy-btn" type="button"><i class="fa fa-copy"></i> Copier</button>');

                                $item.append($urlGroup);
                                $list.append($item);
                            });

                            $section.append($list);
                            $modalBody.append($section);
                        });

                        // Initialize copy buttons
                        $('.copy-btn').on('click', function() {
                            const $input = $(this).closest('.input-group').find('input');
                            $input.select();
                            document.execCommand('copy');

                            const $icon = $(this).find('i');
                            const $text = $(this).contents().last();

                            $(this).removeClass('btn-outline-primary').addClass('btn-success');
                            $icon.removeClass('fa-copy').addClass('fa-check');
                            $text.replaceWith(' Copié !');

                            setTimeout(() => {
                                $(this).removeClass('btn-success').addClass('btn-outline-primary');
                                $icon.removeClass('fa-check').addClass('fa-copy');
                                $text.replaceWith(' Copier');
                            }, 2000);
                        });
                    },
                    error: function(xhr) {
                        let errorMessage = 'Erreur lors du chargement des URLs';
                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        }
                        $modalBody.html('<div class="alert alert-danger">' + errorMessage + '</div>');
                    }
                });
            } else {
                $(this).find('.router-urls-container').html(
                    '<div class="alert alert-warning">Aucune URL de routeur disponible pour cette base</div>'
                );
            }
        });
    }

    static frequencyDays() {
        const $radioFrequencies = $('input[name="base[frequency]"]');
        const $restrictedDays = $('input[name="base[restrictedDays][]"]');
        const $autoCreationDays = $('input[name="base[autoCreationDays][]"]');
        const $autoValidationDays = $('input[name="base[autoValidationDays][]"]');
        const customSplit = (value, separator) => {
            if (typeof value === 'number') { return customSplit(value.toString(), separator); }

            if (typeof value === 'string') {
                if (value === '') { return []; }
                return value.split(separator);
            }

            return [];
        }

        const checkValuesCheckboxes = (valuesToCheck) => {
            [$restrictedDays, $autoCreationDays, $autoValidationDays].forEach( ($group) => {
                $group.prop( "checked", false );
                $group.each(function() {
                    if ($.inArray($(this).val(), valuesToCheck) !== -1) {
                        $(this).prop('checked', true);
                    }
                });
            });
        }

        const updateDaysFromFrequency = (e) => {
            const $checkedRadio = $(e.target);
            let weekDays = customSplit($checkedRadio.data('week-days'), ',');
            checkValuesCheckboxes(weekDays);
        }

        if ($radioFrequencies.length > 1) {
            $radioFrequencies.on('click', updateDaysFromFrequency);
        }
    }
}

window.Base = Base;
