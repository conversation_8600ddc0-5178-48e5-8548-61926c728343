class EditorUtils {
    static initialize() {
        $("#theme li a").on('click', changeTheme);
        $("#view-mobile").on('click', setPreviewMobile);
        $("#view-desktop").on('click', setPreviewDesktop);

        // bootstrap-select: size is not working, fix it using css
        // $('.selectpicker').selectpicker({
        //     size: 5
        // });
        $('.bootstrap-select.btn-group .dropdown-menu.inner').css('max-height', '300px');

        function setPreviewMobile() {
            $("iframe#result").css('width', '320px');
        }

        function setPreviewDesktop() {
            $("iframe#result").css('width', '100%');
        }

        function changeTheme() {
            // set selected css class
            $("#theme li a").removeClass("selected");
            $(this).addClass("selected");

            let theme = $(this).data("value");
            editor.setTheme("ace/theme/" + theme);
        }
    }
}
window.EditorUtils = EditorUtils;
$(function() {
    EditorUtils.initialize();
});