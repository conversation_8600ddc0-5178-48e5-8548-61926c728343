class Planning {
    static initDataTable(config) {
        let table = $('#planning-list').initDataTables(config, {
            searching: true,
            layout: {
                topStart: 'pageLength',
                topEnd: 'search',
                bottomStart: 'info',
                bottomEnd: 'paging'
            },
            language: datatableLanguage,
            initComplete(settings, json) {
                let datatable = this.api().table();
                // Add any custom filters here if needed
            }
        });

        return table;
    };

    static initDeleteAction(datatable) {
        const $deleteModal = $('#delete-modal');
        $deleteModal.on('show.bs.modal', function(e) {
            // set data-url to the delete button
            $(this).find('#confirm-delete').data('url', $(e.relatedTarget).data('url'));
        });

        $deleteModal.find('.btn-danger').on('click', function () {
            const url = $(this).data('url');
            // Blur the button to remove focus before hiding the modal
            this.blur();
            $.ajax({
                url: url,
                type: 'DELETE',
                success: function (result) {
                    alert.success('Suppression effectuée!');
                    Planning.initDataTable(datatable);
                },
                error: function (error) {
                    const err = error.responseJSON;
                    let message = err.detail;
                    if (err.more !== undefined && err.more.error !== undefined) {
                        message += '<hr><p>' + err.more.error + '</p>';
                    }
                    alert.error(message);
                },
                complete: function () {
                    $deleteModal.modal('hide');
                }
            });
        });
    };

    static initSelect2() {
        $('.select2').select2({
            placeholder: 'Sélectionner des bases',
            allowClear: true
        });
    }
}

window.Planning = Planning;
