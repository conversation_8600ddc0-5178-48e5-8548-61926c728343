class Template {
    static initDataTable(config) {
        let typeFilterHtml = `<div class="d-inline-block ml-2">
                    <label>Statut du template : </label>
                    <select id="used-filter" class="form-control-sm">
                        <option value="all">Tous</option>
                        <option value='1'>utilisé</option>
                        <option value='-1'>non utilisé</option>
                    </select>
                    </div>`;

        let table = $('#template-list').initDataTables(config, {
            searching: true,
            // dom:'<"html5buttons"B>lTfgtpi',
            layout: {
                topStart: 'pageLength',
                topEnd: 'search',
                bottomStart: 'info',
                bottomEnd: 'paging'
            },
            language: datatableLanguage,
            initComplete(settings, json) {
                let datatable = this.api().table();
                $(datatable.container()).find('div.dataTables_filter label:first-child').after(typeFilterHtml);
                // init custom handlers
                initDataTableFilter(datatable);
                iniDeleteAction(datatable);
            }
        });

        function initDataTableFilter(datatable) {
            $('#used-filter').on('change', function () {
                let filterValue = $(this).val();
                if (filterValue === "all") {
                    datatable.column(2).data().search('').draw();
                } else {
                    datatable.column(2).data().search(filterValue).draw();
                }
            });
        }

        function iniDeleteAction(datatable) {
            const $deleteModal = $('#delete-modal');
            $deleteModal.on('show.bs.modal', function(e) {
                // set data-url to the delete button
                $(this).find('#confirm-delete').data('url', $(e.relatedTarget).data('url'));
            });

            $deleteModal.find('.btn-danger').on('click', function () {
                const url = $(this).data('url');
                // Blur the button to remove focus before hiding the modal
                this.blur();
                $.ajax({
                    url: url,
                    type: 'DELETE',
                    success: function (result) {
                        alert.success('Suppression effectuée!');
                        datatable.draw();
                    },
                    error: function (error) {
                        const err = error.responseJSON;
                        let message = err.detail;
                        if (err.more !== undefined && err.more.error !== undefined) {
                            message += '<hr><p>' + err.more.error + '</p>';
                        }
                        alert.error(message);
                    },
                    complete: function () {
                        $deleteModal.modal('hide');
                    }
                });
            });
        }
    }

    static initHtmlEditor(backUrl, saveUrl) {
        if ($('.select2').length) {
            $('.select2').select2();
        }

        const $btnBack = $('#btn-editor-back');
        $btnBack.on('click', function () {
            request.redirect(backUrl);
        });

        const $btnSave = $('#btn-editor-save');
        $btnSave.on('click', function () {
            Template.handleSaveClick($btnSave, saveUrl);
        });
    }

    static handleSaveClick($btnSave, saveUrl) {
        const btnHtml = $btnSave.html();
        $btnSave
            .html('<span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span>' + btnHtml)
            .addClass('disabled')
            .prop('disabled', true);

        $.ajax({
            url: saveUrl,
            type: 'PUT',
            data: { content: editor.getValue() },
            success(result) {
                alert.success(result.detail, true);
            },
            error(error) {
                let message = 'Erreur inconnue';
                if (error.responseJSON) {
                    const err = error.responseJSON;
                    message = err.detail;
                    if (err.more?.error) {
                        message += '<hr><p>' + err.more.error + '</p>';
                    }
                }
                alert.error(message);
            },
            complete() {
                $btnSave.html(btnHtml).removeClass('disabled').prop('disabled', false);
            }
        });
    }
}

window.Template = Template;
