class EditorToolbar {
    constructor() {
        this.originalHtml = editor.getValue();
        this.bindEvents();
        this.initSearchReplace();
    }

    bindEvents() {
        $(".insert-value").on('change', this.insertSelectValue);
        $("#tag-insertion li a").on('click', this.insertTag);
        $("#fragment").on('change', this.jumpToFragment);
        $("#btnAdd").on('click', this.addNewTag);
        $("#btn-beautify").on('click', this.beautifySelection);
        $("#btn-fold").on('click', () => this.foldDisabledRanges());
        $("#btn-editor-save").on('click', this.saveContent);

        // Search & Replace
        $('#txt-search').bind('enterKey', () => this.findAll());
        $("#txt-search").on('keyup', this.find);
        $("#find-next").on('click', this.findNext);
        $("#find-previous").on('click', this.findPrevious);
        $("#btn-replace").on('click', this.replace);
        $('#show-history').on('click', this.showHistory);
        $('#search-history').on('click', 'a', this.getFromHistoryList);
    }

    initSearchReplace() {
        const history = SearchHistory.getLast();
        if (history) {
            this.setSearchReplaceValues(history.search, history.replace);
        }
    }

    setSearchReplaceValues(searchText, replaceText) {
        $('#txt-search').val(searchText);
        $('#txt-replace').val(replaceText);
    }

    async foldDisabledRanges() {
        const currentHtml = editor.getValue();
        if (currentHtml !== this.originalHtml) {
            await this.reloadRanges();
        }
        this.doFoldDisabledRanges();
    }

    doFoldDisabledRanges() {
        const ranges = filter.jsonToArray(JSON.parse($("input#ranges").val()));
        editorApp.foldDisabledRanges(ranges);
    }

    beautifySelection() {
        editorApp.beautifySelection();
        this.reloadRanges();
    }

    jumpToFragment(e) {
        const fragment = $(e.currentTarget).find(":selected").text();
        editor.find(fragment);
    }

    insertSelectValue(e) {
        const value = $(e.currentTarget).val();
        editor.insert(value);
    }

    insertTag(e) {
        e.preventDefault();
        console.log('click on tag');
        editor.insert($(e.currentTarget).data("value"));
    }

    addNewTag() {
        const tagName = $('#txtTagName').val();
        editor.insert(
            `<!-- START OF ${tagName} -->\n` +
            `$#${tagName}$#$\n` +
            `<!-- END OF ${tagName} -->\n`
        );
        $('#tagModal').modal('hide');
    }

    findAll() {
        const text = $('#txt-search').val();
        if (text) {
            const matches = editor.findAll(text);
            $("span#matches").html(matches);
        } else {
            editor.forEachSelection({
                exec: () => editor.clearSelection()
            });
        }
    }

    find(e) {
        if (e.keyCode === 13) {
            $("span#matches").html() ? this.findAll() : $(this).trigger("enterKey");
        } else if (e.keyCode === 8) {
            $("span#matches").html("");
        }
    }

    findNext() {
        const text = $('#txt-search').val();
        editor.findNext({needle: text});
    }

    findPrevious() {
        const text = $('#txt-search').val();
        editor.findPrevious({needle: text});
    }

    replace() {
        const findText = $('#txt-search').val();
        const replaceText = $('#txt-replace').val();
        SearchHistory.save({search: findText, replace: replaceText});
        editor.findAll(findText);
        editor.replaceAll(() => replaceText);
    }

    showHistory() {
        const history = SearchHistory.getAll();
        const $history = $('#search-history').empty();
        history.forEach(item => {
            $history.append(`<li><a href="#" class="dropdown-item" data-replace="${item.replace}">${item.search}</a></li>`);
        });
    }

    getFromHistoryList(e) {
        e.preventDefault();
        const $link = $(e.currentTarget);
        this.setSearchReplaceValues($link.html(), $link.data('replace'));
    }

    loadFragments(fragments) {
        const $options = $("#fragment");
        $options.find("option:gt(0)").remove();
        fragments.forEach(fragment => {
            $options.append($("<option />").val(fragment.fragmentName).text(fragment.fragmentName));
        });
    }

    async reloadRanges() {
        const url = $("input#get-ranges-url").val();
        const response = await $.ajax({
            url,
            type: 'POST',
            data: {html: editor.getValue()}
        });
        if (response.ranges) {
            $('input#ranges').val(response.ranges);
        }
        return response;
    }

    refresh(result) {
        const {more} = result;
        if (more.fragments) {
            this.loadFragments(filter.jsonToArray(more.fragments));
        }
        if (more.content?.ranges) {
            this.reloadRanges();
        }
        if (more.check?.messages) {
            this.updateMessages(more.check);
        }
    }

    updateMessages(check) {
        const errCount = check.errors_count || 0;
        const msgCount = (check.messages_count || 0) - errCount;
        $('#errors-count').html(errCount);
        $('#messages-count').html(msgCount);

        const $modal = $('div#messagesModal');
        $modal.find('.check-errors').toggle(errCount > 0)
            .find('h5').html(`Erreurs (${errCount})`);
        $modal.find('.check-messages').toggle(msgCount > 0)
            .find('h5').html(`Messages (${msgCount})`);

        $modal.find('div.content').empty();
        check.messages.forEach(message => {
            const target = message.level === "error" ? '.check-errors' : '.check-messages';
            $(`${target} div.content`, $modal).append(`<pre class="word-wrap">${message.content}</pre>`);
        });
    }
}

class SearchHistory {
    static getAll() {
        return JSON.parse(localStorage.getItem('search-history') || '[]');
    }

    static save(item) {
        const history = this.getAll();
        history.push(item);
        history.slice(-10);
        localStorage.setItem('search-history', JSON.stringify(history));
    }

    static getLast() {
        const history = this.getAll();
        return history.slice(-1)[0];
    }
}
document.addEventListener('DOMContentLoaded', () => {
    const initToolbar = () => {
        if (window.editor && !window.EditorToolbar) {
            // wait till editor is available and prevent duplicate instances
            window.EditorToolbar = new EditorToolbar();
        } else if (!window.editor) {
            setTimeout(initToolbar, 100);
        }
    };
    initToolbar();
});