const datatableLanguage = {
    decimal: ',',
    thousands: '.',
    processing: "Traitement en cours...",
    search: "Rechercher&nbsp;:",
    lengthMenu: "Afficher _MENU_ &eacute;l&eacute;ments",
    info: "Affichage de l'&eacute;lement _START_ &agrave; _END_ sur _TOTAL_ &eacute;l&eacute;ments",
    infoEmpty: "Affichage de l'&eacute;lement 0 &agrave; 0 sur 0 &eacute;l&eacute;ments",
    infoFiltered: "(filtr&eacute; de _MAX_ &eacute;l&eacute;ments au total)",
    infoPostFix: "",
    loadingRecords: "Chargement en cours...",
    zeroRecords: "Aucun &eacute;l&eacute;ment &agrave; afficher",
    emptyTable: "Aucune donnée disponible dans le tableau",
    paginate: {
        first: "Premier",
        previous: "Pr&eacute;c&eacute;dent",
        next: "Suivant",
        last: "Dernier"
    },
    aria: {
        sortAscending: ": activer pour trier la colonne par ordre croissant",
        sortDescending: ": activer pour trier la colonne par ordre décroissant"
    }
}

const alert = {
    success(message = '', slideUp = false) {
        const $alert = $(".alert-js");
        $alert.removeClass('alert-danger').addClass('alert-success')
        if (slideUp) {
            $alert.fadeTo(3000, 500).slideUp(500, function(){
                $alert.slideUp(500);
                $alert.hide();
            });
        } else {
            $alert.show();
        }
        $alert.find(".alert-danger-icon").hide();
        $alert.find(".alert-info-icon").show();
        $alert.find(".alert-message").html(message);
    },

    error(message = '', slideUp = false) {
        const $alert = $(".alert-js");
        $alert.removeClass('alert-success').addClass('alert-danger');
        if (slideUp) {
            $alert.fadeTo(3000, 500).slideUp(500, function () {
                $alert.slideUp(500);
                $alert.hide();
            });
        } else {
            $alert.show();
        }
        $alert.find(".alert-info-icon").hide();
        $alert.find(".alert-danger-icon").show();
        $alert.find(".alert-message").html(message);
    },

    hide() {
        $(".alert-js").hide();
    }
};

const request = {
    redirect(action) {
        document.location.href = action;
    },

    openBlank(url) {
        const win = window.open(url, '_blank');
        if (win) {
            win.focus();
        }
    }
};