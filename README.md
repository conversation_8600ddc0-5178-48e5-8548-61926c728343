Rogue-One
=========

Projet de refonte de pcp-manager.

Outils de gestion de l'activité de la Data-Room :
* Intégration des créa annonceurs
* Planification des envois email
* Suivi de la performance des envois/campagnes/routeurs/plate-forme/annonceurs
* Pilotage de la rentabilité de l'activité

Dossier projet : [google drive](https://drive.google.com/drive/u/0/folders/0B1A5NPf4-NW6Y01RSkFfanBGZ3c)

## Installation using Composer

Clone the project form git and use composer to install dependencies.

```bash
docker exec -it phpengine7 /bin/bash
cd /opt/rogue-one
composer install
```
In case of having an error running composer install:
```
fatal: detected dubious ownership in repository at '/Users/<USER>/Code/rogue-one-sf/vendor/it-data/user-bundle'
```
try this:
```
git config --global --add safe.directory /Users/<USER>/Code/rogue-one-sf/vendor/it-data/user-bundle
```
## Init Database

You need to have a postgres database (preferable version 9.6).
- If you are using docker you need to copy the sql file to your postgres container first:

```bash
cd build/resources/dump/
unzip rogue_one_with_data_20190812.sql.zip
docker cp rogue_one_with_data_20190812.sql postgres:/tmp/rogue_one_with_data_20190812.sql
```

- Create an empty database and name it as follows:

    PS: if you are using docker you need to log into db container first:
```bash
docker exec -ti postgres bash
```

then create the database:

```bash
psql -h localhost -U root -c "CREATE DATABASE rogue_one;"
```

- Restore database from dump:

```bash
psql -h localhost -U root rogue_one < /tmp/rogue_one_with_data_20190812.sql
```

- make sure you have the right db config in `config/autoload/doctrine.config.php` (or `config/autoload/vagrant/doctrine.config.php`)

You are ready to use Rogue-One :)