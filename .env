###> symfony/framework-bundle ###
APP_ENV=dev
APP_SECRET=630a4dec5f7fc07ceeed36fd7220b0c2
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
#
# DATABASE_URL="sqlite:///%kernel.project_dir%/var/data.db"
# DATABASE_URL="mysql://app:!ChangeMe!@127.0.0.1:3306/app?serverVersion=8&charset=utf8mb4"
DATABASE_URL="postgresql://app:!ChangeMe!@127.0.0.1:5432/app?serverVersion=15&charset=utf8"
###< doctrine/doctrine-bundle ###

###> google/apiclient ###
GOOGLE_API_KEY=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_AUTH_CONFIG=%kernel.project_dir%/path/to/file.json
###< google/apiclient ###
PSQL_DB=${PSQL_DB}
PSQL_VERSION=${PSQL_VERSION}
PSQL_PORT=${PSQL_PORT}
PSQL_SOCKET=${PSQL_SOCKET}
PSQL_USER=${PSQL_USER}
PSQL_PASSWORD=${PSQL_PASSWORD}

###> symfony/messenger ###
# Choose one of the transports below
# MESSENGER_TRANSPORT_DSN=amqp://guest:guest@localhost:5672/%2f/messages
# MESSENGER_TRANSPORT_DSN=redis://localhost:6379/messages
MESSENGER_TRANSPORT_DSN=doctrine://default?auto_setup=0
###< symfony/messenger ###

###> symfony/mailer ###
# MAILER_DSN=null://null
###< symfony/mailer ###

###> symfony/google-mailer ###
# Gmail SHOULD NOT be used on production, use it in development only.
# MAILER_DSN=gmail://USERNAME:PASSWORD@default
###< symfony/google-mailer ###
