{"type": "project", "license": "proprietary", "minimum-stability": "dev", "prefer-stable": true, "require": {"php": ">=8.3", "ext-ctype": "*", "ext-iconv": "*", "ext-pdo_pgsql": "*", "ext-pgsql": "*", "composer/package-versions-deprecated": "*", "doctrine/annotations": "*", "doctrine/cache": "*", "doctrine/common": "*", "doctrine/doctrine-bundle": "*", "doctrine/doctrine-migrations-bundle": "*", "doctrine/instantiator": "*", "doctrine/lexer": "*", "doctrine/orm": "^3.0@beta", "doctrine/persistence": "*", "dompdf/dompdf": "*", "google/apiclient": "^2.14", "google/cloud-bigquery": "^1.23", "google/cloud-logging": "^1.26", "google/cloud-storage": "^1.30", "google/common-protos": "*", "greenlion/php-sql-parser": "*", "it-data/user-bundle": "dev-master", "league/commonmark": "^2.5", "omines/datatables-bundle": "^0.10.1", "phpdocumentor/reflection-docblock": "*", "phpstan/phpdoc-parser": "*", "psr/container": "*", "psr/log": "*", "symfony/asset": "7.2.*", "symfony/asset-mapper": "7.2.*", "symfony/cache": "7.2.*", "symfony/console": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/expression-language": "7.2.*", "symfony/flex": "*", "symfony/form": "7.2.*", "symfony/framework-bundle": "7.2.*", "symfony/google-mailer": "7.2.*", "symfony/http-client": "7.2.*", "symfony/intl": "7.2.*", "symfony/mailer": "7.2.*", "symfony/monolog-bundle": "*", "symfony/property-access": "7.2.*", "symfony/runtime": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/serializer": "7.2.*", "symfony/stimulus-bundle": "^2.24", "symfony/string": "7.2.*", "symfony/translation": "7.2.*", "symfony/twig-bundle": "7.2.*", "symfony/ux-chartjs": "^2.24", "symfony/validator": "7.2.*", "symfony/web-link": "7.2.*", "symfony/workflow": "7.2.*", "symfony/yaml": "7.2.*", "twig/extra-bundle": "*", "twig/intl-extra": "^3.8", "twig/markdown-extra": "^3.13", "twig/twig": "*"}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^3.6", "friendsofphp/php-cs-fixer": "*", "phpstan/phpstan-symfony": "*", "phpunit/phpunit": "*", "rector/rector": "*", "symfony/browser-kit": "7.2.*", "symfony/css-selector": "7.2.*", "symfony/debug-bundle": "7.2.*", "symfony/maker-bundle": "*", "symfony/phpunit-bridge": "*", "symfony/stopwatch": "7.2.*", "symfony/web-profiler-bundle": "7.2.*"}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "symfony/runtime": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true, "process-timeout": 0}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"cs-fix": "tools/php-cs-fixer/vendor/bin/php-cs-fixer fix src", "cs-check": "tools/php-cs-fixer/vendor/bin/php-cs-fixer fix src --dry-run --stop-on-violation --using-cache=no", "phpstan-analyse": "vendor/bin/phpstan analyse src tests", "auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}, "repositories": [{"type": "git", "url": "https://vendor-it-data-user-bundle:<EMAIL>/prismamediadata/vendor/it-data-user-bundle.git"}]}